.columnsDrawer {
  .header {
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 16px;
  }

  .content {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 70px;

    .group {
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .groupTitle {
      font-size: 14px;
      font-weight: 500;
      color: #333;
      padding: 8px 12px;
      background: linear-gradient(90deg, #f0f2f5 0%, #fafafa 100%);
      border: 1px solid #d9d9d9;
      border-radius: 6px 6px 0 0;
      margin-bottom: 0;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .anticon {
        color: #1890ff;
      }

      .title-text {
        display: flex;
        align-items: center;
      }
    }

    .listItem {
      padding: 8px 0;
      border-bottom: 1px solid #f0f0f0;
      cursor: move;

      &:last-child {
        border-bottom: none;
      }

      &:hover {
        background-color: #f5f5f5;
      }
    }

    .ant-list {
      border: 1px solid #d9d9d9;
      border-top: none;
      border-radius: 0 0 6px 6px;
      background-color: #fff;

      .ant-list-item {
        padding: 8px 12px;
        margin: 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }
      }
    }

    .ant-btn-group {
      .ant-btn {
        padding: 0 6px;
        height: 24px;
        line-height: 22px;
      }
    }

    .operation-buttons {
      margin-left: 8px;
    }
  }

  .footer {
    padding: 16px 24px;
    border-top: 1px solid #f0f0f0;
    background-color: #fff;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 10;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);
  }

  .ant-drawer-body {
    display: flex;
    flex-direction: column;
    padding: 24px;
    padding-bottom: 0;
    overflow: hidden;
  }
}
