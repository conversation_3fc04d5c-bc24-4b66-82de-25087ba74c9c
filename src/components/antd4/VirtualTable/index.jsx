import { Table } from 'antd4';
import classNames from 'classnames';
import ResizeObserver from 'rc-resize-observer';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { VariableSizeGrid as Grid } from 'react-window';
import { ConfigProvider } from 'antd4';
import zhCN from 'antd4/es/locale/zh_CN';
import styles from './index.less';

import 'antd4/es/table/style/index.less'
import 'antd4/es/empty/style/index.less'
import 'antd4/es/spin/style/index.less'
import 'antd4/es/pagination/style/index.less'
import 'antd4/es/select/style/index.less'

const VirtualTable = (props) => {
    const { columns, scroll, dataSource, rowHeight = 54 } = props;
    const [tableWidth, setTableWidth] = useState(0);

    const widthColumnCount = columns.filter(({ width }) => !width).length;
    const mergedColumns = columns.map((column) => {
        if (column.width) {
            return column;
        }
        return {
            ...column,
            width: Math.floor(tableWidth / widthColumnCount),
        };
    });
    const gridRef = useRef();
    const [connectObject] = useState(() => {
        const obj = {};
        Object.defineProperty(obj, 'scrollLeft', {
            get: () => {
                if (gridRef.current) {
                    return gridRef.current?.state?.scrollLeft;
                }
                return null;
            },
            set: (scrollLeft) => {
                if (gridRef.current) {
                    gridRef.current.scrollTo({
                        scrollLeft,
                    });
                }
            },
        });
        return obj;
    });

    const resetVirtualGrid = () => {
        gridRef.current?.resetAfterIndices({
            columnIndex: 0,
            shouldForceUpdate: true,
        });
    };

    useEffect(() => resetVirtualGrid, [tableWidth]);

    const renderVirtualList = (rawData, { scrollbarSize, ref, onScroll }) => {
        ref.current = connectObject;
        const totalHeight = rawData.length * rowHeight;
        return (
            <Grid
                ref={gridRef}
                className="virtual-grid"
                columnCount={mergedColumns.length}
                columnWidth={(index) => {
                    const { width } = mergedColumns[index];
                    return totalHeight > scroll.y && index === mergedColumns.length - 1
                        ? width - scrollbarSize - 1
                        : width;
                }}
                height={scroll.y}
                rowCount={rawData.length}
                rowHeight={() => rowHeight}
                width={tableWidth}
                onScroll={({ scrollLeft }) => {
                    onScroll({
                        scrollLeft,
                    });
                }}
            >
                {({ columnIndex, rowIndex, style }) => {
                    const { render, dataIndex, align } = mergedColumns[columnIndex];
                    const record = rawData[rowIndex];
                    const value = record[dataIndex];
                    const renderValue = render
                        ? dataIndex
                            ? render(value, record, columnIndex)
                            : render(record, columnIndex)
                        : value;

                    const styleMerge = {
                        ...style,
                        textAlign: align
                    }

                    return (
                        <div
                            className={classNames('virtual-table-cell', {
                                'virtual-table-cell-last': columnIndex === mergedColumns.length - 1,
                            })}
                            style={styleMerge}
                        >
                            {renderValue}
                        </div>
                    )
                }}
            </Grid>
        );
    };


    // 超过200才使用虚拟滚动
    const components = useMemo(() => {
        const needVirtual = dataSource && dataSource.length > 200;
        return needVirtual
            ? {
                body: renderVirtualList,
            }
            : {}
    }, [dataSource]);

    const rootCls = classNames(styles.virtualTable, 'ant4');

    return (
        <ConfigProvider locale={zhCN}>
            <div
                className={rootCls}
            >
                <ResizeObserver
                    onResize={({ width }) => {
                        setTableWidth(width);
                    }}
                >
                    <Table
                        {...props}
                        columns={mergedColumns}
                        components={components}
                    />
                </ResizeObserver>
            </div>
        </ConfigProvider>
    );
};

export default VirtualTable;