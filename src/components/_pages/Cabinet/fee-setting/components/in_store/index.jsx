/* eslint-disable no-confusing-arrow */
/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import React, { useEffect, useState } from 'react';
import { connect } from 'dva';
import { Alert, message } from 'antd';
import ServiceTable from './table';
import styles from './style.less';
import ServiceBrandSetFee from '@/pages/Post/Dispat/components/ServiceBrandSetFee';
import { getDispatchFee } from '@/services/dispat';

const CabinetSetInStore = ({ setting }) => {
  const [serviceSettings, setServiceSettings] = useState({});
  const getDispatchFeeHandle = async () => {
    const { code, data, msg } = await getDispatchFee({});
    const status = `${code}` === '0' && !!data;
    setServiceSettings(status ? data : {});
    if (!status) {
      message.error(msg);
    }
  };

  useEffect(() => {
    getDispatchFeeHandle();
  }, []);

  return (
    <div style={{ paddingTop: 10 }} className={styles.account}>
      <div style={{ marginBottom: 20 }}>
        <Alert
          message={
            <span>
              {`1.如设置的入库服务费价格为X元，则加盟商快递柜每入库一票快件，从加盟商下属快递柜的${
                setting.options.postName
              }驿站APP可消费账户扣除X元至${setting.options.postName}智慧快递管理平台资金账户内。`}
              <br />
            </span>
          }
          type="warning"
        />
      </div>
      <ServiceBrandSetFee showCabinet feeData={serviceSettings} onSuccess={getDispatchFeeHandle} />
      <ServiceTable />
    </div>
  );
};

export default connect(store => {
  const { setting } = store;
  return {
    setting,
  };
})(CabinetSetInStore);
