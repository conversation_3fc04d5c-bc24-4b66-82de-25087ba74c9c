/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Card, Col, Row, Typography } from 'antd';
import React, { useMemo } from 'react';
import { connect } from 'dva';
import { checkIsWxWork } from '@/utils/navigator';
import DaoSetting from './setting';
import DaoConfig from './config';
import ReceiveSetting from './receiveSetting';
import TimeSetting from './timeSetting';
import RemindSetting from './remindSetting';
import KBSwitch from './KBSwitch';
import { useScan } from './useDeliveryScan';
import Discount from './discount';
import AllowSecond from './allowSecond';
import BrandSetting from './brandSetting';
import ArriveDispatch from './arriveDispatch';
import ArriveDispatchSetting from './arriveDispatchSetting';

const { Text } = Typography;

const DeliveryScan = ({ options = {} }) => {
  const { key } = options;
  // const { user_info: { branchLevel } = {} } = currentUser;

  const isYZ = key == 'yz';
  const isZy = key == 'post';
  // const isZYXJ = isZy && branchLevel == '3'; // 中邮 县级
  const kb = isYZ ? '快宝' : '';
  const fn = useScan();

  const settingArray = useMemo(
    () => {
      const isWxWork = checkIsWxWork();
      return [
        {
          title: '派件自动补扫到件',
          desc: `开启后业务员做派件上传，将检查单号是否在${kb}共配系统中做过到件上传，如未做到件，将自动补扫到件`,
          key: 0,
          child: <DaoConfig {...fn} />,
          setting: props => <DaoSetting {...props} />,
        },
        {
          title: '签收自动补扫派件',
          desc: `开启后业务员做签收上传，将检查单号是否在${kb}共配系统中做过派件上传，如未做派件，将自动补扫派件`,
          key: 1,
          child: <ReceiveSetting {...fn} />,
          setting: props => <DaoSetting {...props} />,
        },
        {
          title: '上传时检查扫描时间',
          desc:
            '开启后，巴枪上传时讲检查单号的扫描时间与上传时间是否超过设定的时间，超过上传时弹窗提示',
          key: 2,
          child: <TimeSetting {...fn} />,
          setting: props => <DaoSetting {...props} />,
        },
        {
          title: '特殊件提醒设置',
          desc: '设置扫描时哪些特殊件类型弹窗提醒',
          key: 3,
          child: <RemindSetting {...fn} />,
          setting: props => <DaoSetting {...props} />,
        },
        {
          title: '圆通允许再次派件',
          desc: '开启后，已做派件不做拦截，支持改派',
          key: 4,
          child: <></>,
          setting: props => <KBSwitch {...props} value={props.yt_repeat_pi} type={4} />,
        },
        {
          title: '重复扫描提醒',
          desc: '开启后，扫描时如检查到单号最近七天内做过同样类型的扫描上传，将弹窗提示',
          key: 5,
          child: <AllowSecond {...fn} type="16" keys={['record_repeat_check']} />,
          setting: props => <KBSwitch {...props} value={props.record_repeat_check} type={5} />,
        },
        {
          title: '禁止业务员修改混合扫描的上传类型',
          desc: '开启后，扫描时不允许业务员修改混合扫描的上传类型',
          key: 6,
          child: <></>,
          setting: props => (
            <KBSwitch {...props} value={props.forbid_change_mixed_upload_type} type={6} />
          ),
        },
        {
          title: '同一单号禁止同派件员二派',
          desc: '开启后，已做派件的单号禁止同派件员二次上传派件（工号一致视为同派件员）',
          key: 7,
          child: (
            <AllowSecond
              {...fn}
              type="8"
              keys={[
                'sto_deny_repeat_pi',
                'zt_deny_repeat_pi',
                'yt_deny_repeat_pi',
                'yd_deny_repeat_pi',
                'jt_deny_repeat_pi',
              ]}
            />
          ),
          setting: props => (
            <BrandSetting
              {...props}
              brands={[
                { name: '申通', value: 'sto_deny_repeat_pi', type: 7 },
                { name: '中通', value: 'zt_deny_repeat_pi', type: 9 },
                { name: '圆通', value: 'yt_deny_repeat_pi', type: 10 },
                { name: '韵达', value: 'yd_deny_repeat_pi', type: 11 },
                { name: '极兔', value: 'jt_deny_repeat_pi', type: 12 },
              ]}
            />
          ),
          hide: !isWxWork && process.env.NODE_ENV != 'development',
        },
        {
          title: '同单号到件与派件隔天操作规则',
          desc: <ArriveDispatch {...fn} />,
          key: 9,
          setting: props => <ArriveDispatchSetting {...props} />,
        },
        {
          title: '签收位置图片强制上传',
          desc: '开启后，对应品牌的签收位置图片强制上传',
          key: 8,
          child: <></>,
          setting: props => (
            <BrandSetting
              {...props}
              brands={[
                { name: '圆通', value: 'yt_sign_img_force_upload', type: 14 },
                { name: '韵达', value: 'yd_sign_img_force_upload', type: 15 },
              ]}
            />
          ),
        },
      ]
        .filter(item => !item.hide)
        .filter(item => !isZy || (isZy && item.key == 4));
    },
    [isZy, fn],
  );

  return (
    <>
      <Discount />
      {settingArray.map(item => (
        <Card bordered={false} key={item.key}>
          <Row type="flex" align="middle" justify="space-between" style={{ height: '70px' }}>
            <Col span={10}>
              <Row justify="start" align="middle" gutter={[10, 10]}>
                <Col span={24} key={item.key}>
                  <Text strong>{item.title}</Text>
                </Col>
                <Col span={24}>
                  {item.desc} {item.child}
                </Col>
              </Row>
            </Col>
            <Col span={13} offset={1}>
              <Row type="flex" justify="end" align="middle">
                {item.setting?.({ type: item.key, ...fn })}
              </Row>
            </Col>
          </Row>
        </Card>
      ))}
    </>
  );
};

export default connect(({ setting, user }) => ({
  options: setting.options,
  currentUser: user.currentUser,
}))(DeliveryScan);
