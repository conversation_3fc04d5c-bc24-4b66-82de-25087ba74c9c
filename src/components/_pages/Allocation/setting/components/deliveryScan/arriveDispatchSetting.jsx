/* eslint-disable react/no-array-index-key */
/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import { editDaopaiSwitch } from '@/services/allocation_setting';
import { Col, Row, Select } from 'antd';
import React, { useEffect, useState } from 'react';

const ArriveDispatchSetting = props => {
  const { arrive_dispatch_operation_rule_status, arrive_dispatch_operation_rule_setting } = props;
  const [status, setStatus] = useState(null);
  const [setting, setSetting] = useState(null);

  useEffect(
    () => {
      setStatus(arrive_dispatch_operation_rule_status == '1' ? '1' : '0');
      setSetting(
        arrive_dispatch_operation_rule_setting
          ? String(arrive_dispatch_operation_rule_setting)
          : null,
      );
    },
    [arrive_dispatch_operation_rule_status, arrive_dispatch_operation_rule_setting],
  );

  const statusOptions = [{ label: '不限制', value: '0' }, { label: '限制', value: '1' }];
  const settingOptions = [
    { label: '2天', value: '2' },
    { label: '3天', value: '3' },
    { label: '4天', value: '4' },
    { label: '5天', value: '5' },
    { label: '6天', value: '6' },
    { label: '7天', value: '7' },
  ];
  const defaultSetting = '2';

  const onChange = (key, value) => {
    editDaopaiSwitch({
      type: 13,
      switch: key == 'settingOptions' ? 1 : value,
      setting: key == 'statusOptions' && value == '1' ? defaultSetting : value,
    }).then(res => {
      res && props.getSwitchReload && props.getSwitchReload();
    });
  };

  return (
    <Row type="flex" justify="space-between" align="middle" gutter={[10, 10]}>
      {(status == '1' ? ['statusOptions', 'settingOptions'] : ['statusOptions']).map(options => (
        <Col key={options}>
          <Select
            onChange={v => onChange(options, v)}
            value={options == 'statusOptions' ? status : setting}
            style={{ width: '88px' }}
          >
            {(options == 'statusOptions' ? statusOptions : settingOptions).map(item => (
              <Select.Option key={item.value} value={item.value}>
                {item.label}
              </Select.Option>
            ))}
          </Select>
        </Col>
      ))}
    </Row>
  );
};

export default ArriveDispatchSetting;
