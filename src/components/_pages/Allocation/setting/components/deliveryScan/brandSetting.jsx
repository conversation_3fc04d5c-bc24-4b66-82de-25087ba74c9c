/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import React from 'react';
import { Row, Col } from 'antd';
import KBSwitch from './KBSwitch';

/**
 * 快递品牌设置组件
 * @param {Object} props - 组件属性
 * @param {Array} props.brands - 品牌配置数组，格式为 [{name: '品牌名', value: 'value_key', type: 开关类型}]
 * @returns {React.ReactNode}
 */
const BrandSetting = ({ brands = [], ...restProps }) => {
  return (
    <Row type="flex" align="middle" gutter={[16, 16]}>
      {brands.map((brand, index) => (
        <React.Fragment key={brand.type || index}>
          <Col>{brand.name}</Col>
          <Col>
            <KBSwitch {...restProps} value={restProps[brand.value]} type={brand.type} />
          </Col>
        </React.Fragment>
      ))}
    </Row>
  );
};

export default BrandSetting;
