/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import React from 'react';
import ProModalFormExtend from '@/components/ProModalFormExtend';
import ProTable from '@/components/AdaptTable';
import { Button } from 'antd';
import { getMixInHistoryList } from '@/services/setup';
import AdaptWrapperFn from '@/components/AdaptForm/form/AdaptWrapper';

const HistoryModal = ({ cm_id, isZyAccount }) => {
  const columns = [
    {
      title: '修改内容',
      dataIndex: 'title',
    },
    {
      title: '修改前',
      dataIndex: 'old',
    },
    {
      title: '修改后',
      dataIndex: 'new',
    },
    {
      title: '修改人',
      dataIndex: 'operate_name',
    },
    {
      title: '修改时间',
      dataIndex: 'create_at',
    },
  ];

  const request = async params => {
    return getMixInHistoryList({
      ...params,
      isZyAccount,
    });
  };

  return (
    <ProModalFormExtend
      title="修改记录"
      width={700}
      trigger={<Button type="primary">修改记录</Button>}
    >
      <ProTable
        rowKey="create_at"
        columns={columns}
        request={request}
        params={{ cm_id }}
        pagination={{
          showQuickJumper: false,
          showSizeChanger: false,
        }}
        search={false}
        scroll={{ y: 555 }}
        footer={false}
      />
    </ProModalFormExtend>
  );
};

export default AdaptWrapperFn(HistoryModal);
