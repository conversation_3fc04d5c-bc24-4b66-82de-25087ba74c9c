/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import React, { useEffect, useImperativeHandle, useState } from 'react';
import { reportFetchPic, searchDetailNew, searchNew } from '@/services/advanced';
import { dateCalendar, isLegalData } from '@/utils/utils';
import { Avatar, Col, Icon, message, Modal, Row } from 'antd';
import moment from 'moment';
import router from 'umi/router';
import KbPreviewImage from '@/components/KbPreviewImage';
import JsBarcode from 'jsbarcode';
import { noop, isPlainObject } from 'lodash';
import { codeTypeOptions } from '@/pages/Automatic/components/SortingModelModal';
import { findCurrentGunScanRecordTab } from '@/pages/Allocation/components/_utils';

export const useSearchResult = (props, ref) => {
  const { type } = props;
  const [expressInfo, setExpressInfo] = useState(null);
  const [activeId, setActiveId] = useState(null);
  const [getting, setGetting] = useState(false);
  const [result, setResult] = useState(null);
  const [loading, setLoading] = useState(false);
  const [barcode, setBarCode] = useState([]);

  const showExpressModal = data => {
    const { brand, brand_cn, waybill_no } = data;
    setExpressInfo({
      brand,
      waybill_no,
      brand_name: brand_cn,
    });
  };

  const showDetail = async (item, index, _data) => {
    const { cm_id, dak_id, brand, waybill_no: waybill, id } = item;
    setActiveId(id);
    setGetting(true);
    try {
      const { code, data, msg } = await searchDetailNew({
        cm_id: cm_id || dak_id, // 兼容共配数据
        waybill,
        brand,
      });
      setGetting(false);
      if (code > 0 || !data) {
        message.error(msg);
        return;
      }

      const _result = { ...(result || _data) };
      const _yz_data_list = _result.yz_data_list || [];
      _yz_data_list.splice(index, 1, {
        ..._yz_data_list[index],
        operates: patchIndex(isLegalData(data?.list)),
      });
      _result.yz_data_list = _yz_data_list;
      setResult(_result);
      return _result;
    } catch (error) {
      setGetting(false);
    }
  };

  const getResult = async params => {
    return new Promise((resolve, reject) => {
      setLoading(true);
      searchNew(params)
        .then(res => {
          setLoading(false);
          const { code, data, msg } = res || {};
          if (code == 0) {
            const { yz_data_list: _data } = data || {};
            setResult(patchIndex(data));
            setBarCode((Array.isArray(_data) ? _data : []).map(item => item.waybill_no));
            if (_data.length > 0 && type == 1) {
              showDetail(_data[_data.length - 1], _data.length - 1, data);
            }
            resolve(_data);
          } else {
            message.error(msg);
            reject();
          }
        })
        .catch(() => {
          setLoading(false);
          reject();
        });
    });
  };

  useEffect(
    () => {
      if (barcode.length) {
        barcode.forEach(item => {
          JsBarcode(`#barcode_${item}`, item, {
            height: 70,
            margin: 0,
            displayValue: false,
          });
        });
      }
    },
    [barcode],
  );

  useImperativeHandle(
    ref,
    () => ({
      getResult,
      showDetail,
    }),
    [],
  );

  return {
    showExpressModal,
    showDetail,
    activeId,
    expressInfo,
    loading,
    getting,
    result,
    setExpressInfo,
  };
};

const wayBillTypeMap = {
  6: 'send',
  5: 'arrive',
  2: 'delivery',
  3: 'sign',
  4: 'error',
};
export const jumpType = Object.keys(wayBillTypeMap);
export const yzColumns = [
  {
    title: '时间',
    dataIndex: 'create_time',
    width: '25%',
    render: dateCalendar,
  },
  {
    title: '类型',
    dataIndex: 'desc',
    width: '25%',
    render: (text, record) => {
      const { picture, picture_invalid, avatar_pic } = record;
      if (picture_invalid === '' || picture_invalid === undefined) {
        return <span style={{ marginRight: 10 }}>{text}</span>;
      }

      if (picture_invalid == 1) {
        return (
          <>
            <span style={{ marginRight: 10 }}>{text}</span>
            <Avatar
              shape="square"
              size="large"
              onClick={() =>
                Modal.info({
                  title: '该底单照片已过期，你可扩充云存储空间后查看',
                })
              }
            />
          </>
        );
      }
      const pic = [picture, avatar_pic].filter(Boolean);

      return (
        <>
          <span style={{ marginRight: 10 }}>{text}</span>
          {pic.length > 0 && <KbPreviewImage src={pic} />}
        </>
      );
    },
  },
  {
    title: '驿站/快递柜',
    dataIndex: 'inn_name',
    width: '25%',
  },
  {
    title: '操作员',
    dataIndex: 'operator',
    width: '25%',
  },
];
export const gpColumns = (brand, { onClose = noop } = {}) => [
  {
    title: '扫描时间',
    dataIndex: 'scan_time',
    width: 188,
    // render: create_time => {
    //   return dateCalendar(create_time, true);
    // },
  },
  {
    title: '类型',
    dataIndex: 'waybill_type_name',
    width: 250,
    render: (desc, record) => {
      const {
        waybill_type,
        sign_pic,
        waybill_type_name,
        create_time,
        waybill_no,
        courier_phone,
      } = record;

      const onJump = hash => {
        const timeFormat = moment(create_time).format('YYYY-MM-DD');

        if (hash && brand) {
          onClose();
          const { tab = hash } = findCurrentGunScanRecordTab(hash) || {};
          router.push({
            pathname: '/Allocation/GunScanRecord',
            hash: `#${hash}`,
            query: {
              brand,
              courier: courier_phone,
              cm_phone: courier_phone,
              start_scan_time: timeFormat,
              end_scan_time: timeFormat,
              waybill_no,
              tabname: `${tab} - ${waybill_no}`,
            },
          });
        }
      };

      const renderText = () => {
        return jumpType.includes(waybill_type) ? (
          <a onClick={() => onJump(wayBillTypeMap[waybill_type])}>{waybill_type_name}</a>
        ) : (
          waybill_type_name
        );
      };

      return (
        <>
          <span style={{ marginRight: 10 }}>{renderText()}</span>
          {sign_pic && <KbPreviewImage src={sign_pic} />}
        </>
      );
    },
  },
  // {
  //   title: '类型对象',
  //   dataIndex: 'waybill_type',
  //   width: '25%',
  //   render: (waybill_type, record) => {
  //     const {
  //       inn_name,
  //       next_station_name,
  //       sign_man,
  //       bad_waybill_type,
  //       delivery_phone,
  //       delivery_name,
  //     } = record;
  //     switch (waybill_type) {
  //       case '2':
  //         return `派件员：${delivery_name || ''} ${delivery_phone || ''}`;
  //       case '3':
  //         return `签收人：${sign_man || ''}`;
  //       case '4':
  //         return `问题件类型：${bad_waybill_type || ''}`;
  //       case '5':
  //         return '';
  //       case '6':
  //         return `下一站：${next_station_name || ''}`;
  //       default:
  //         return `驿站：${inn_name || ''}`;
  //     }
  //   },
  // },
  {
    title: '特殊件',
    dataIndex: 'notice_type_name',
    width: 200,
  },
  {
    title: '入库时间',
    dataIndex: 'create_time',
    width: 288,
  },
  {
    title: '推送时间',
    dataIndex: 'update_time',
    width: 288,
  },
  {
    title: '是否开启自动上传',
    dataIndex: 'auto_upload',
    width: 100,
    render: text => (text == 1 ? '已开启' : '未开启'),
  },
  {
    title: '上传推送',
    dataIndex: 'send_status',
    width: 100,
    render: (text, { fail_desc }) => {
      return text == 0 ? '待处理' : text == 1 ? '成功' : `失败${fail_desc}`;
    },
  },
  {
    title: '操作平台',
    dataIndex: 'operation_platform',
    width: 100,
    render: text => (
      <Row gutter={12} type="flex" align="middle">
        <Col>{text}</Col>
      </Row>
    ),
  },
  {
    title: '业务员',
    dataIndex: 'operator',
    width: 250,
    render: (_, record) => {
      const { cm_name, cm_phone } = record;
      return `${cm_name || ''} ${cm_phone || ''}`;
    },
  },
];

const sortTypeMap = {
  0: '标准',
  1: '三段码',
  2: '地址分拣',
  12: '先三段码 再地址',
  21: '先地址 再三段码',
};

const onPreview = ({ waybill, id }) => {
  reportFetchPic({
    waybill,
    id,
  });
};

export const sortingColumns = [
  {
    title: '扫描',
    dataIndex: 'gbt_scan_time',
    width: 240,
    render: (
      _,
      {
        gbt_scan_time,
        match_data: { file_path = '', file_path_online, id: _id } = {},
        id,
        waybill,
      },
    ) => {
      let filePath = file_path;
      if (filePath.indexOf('local_pic') > -1) {
        filePath = `local_pic${_id}`;
      } else if (!filePath.startsWith('http')) {
        filePath = '';
      }
      return (
        <Row type="flex" align="middle">
          <Col>{gbt_scan_time}</Col>
          <Col>
            {filePath ? (
              <KbPreviewImage src={filePath} type="custom" unReg>
                <Icon type="file-image" style={{ fontSize: '28px' }} />
              </KbPreviewImage>
            ) : file_path_online == 1 ? (
              <Icon
                type="file-image"
                style={{ fontSize: '28px' }}
                onClick={() => onPreview({ waybill, id })}
              />
            ) : null}
          </Col>
        </Row>
      );
    },
  },
  {
    title: '分拣模式',
    dataIndex: 'sort_type',
    width: 400,
    render: (_, { match_data: { sort_type, other_data } = {} }) => {
      let other;
      try {
        other = JSON.parse(other_data);
      } catch (error) {
        other = {};
      }
      const { config: { get3CodeType, fourCode } = {} } = other;
      return (
        <Row type="flex" align="middle">
          <Col>{sortTypeMap[sort_type] ? `${sortTypeMap[sort_type]}` : sort_type}</Col>
          {sort_type != 2 && (
            <>
              <Col style={{ margin: '0 8px' }}>|</Col>
              <Col>{fourCode == 1 ? '分拣四段码' : '不分拣四段码'}</Col>
            </>
          )}
          {get3CodeType && (
            <>
              <Col style={{ margin: '0 8px' }}>|</Col>
              <Col>{codeTypeOptions.find(item => item.value == get3CodeType)?.label}</Col>
            </>
          )}
        </Row>
      );
    },
  },
  {
    title: '使用段码',
    dataIndex: 'api_3code',
    width: 300,
    render: (_, record) => {
      const { use, useDesc, useTime } = get3CodeType(record);
      return (
        <div>
          <div>{use}</div>
          <div>{useDesc}</div>
          <div>{useTime}</div>
        </div>
      );
    },
  },
  {
    title: '分配格口',
    dataIndex: 'grid_code',
  },
  {
    title: '实际格口',
    dataIndex: 'actual_grid_code',
  },
  {
    title: '识别状态',
    dataIndex: 'gbt_status',
  },
  {
    title: '未用段码',
    dataIndex: 'match_data',
    width: 300,
    render: (_, record) => {
      const { unUse, unUseDesc, unUseTime } = get3CodeType(record);
      return (
        <div>
          <div>{unUse}</div>
          <div>{unUseDesc}</div>
          <div>{unUseTime}</div>
        </div>
      );
    },
  },
  {
    title: '地址关键词',
    dataIndex: 'addr_keys',
  },
  {
    title: '特殊件',
    dataIndex: 'remind_type',
    render: (_, record) => {
      const { match_data = {} } = record;
      return match_data.remind_type;
    },
  },
];

const get3CodeType = record => {
  const {
    match_data: {
      match_form,
      three_code_form,
      api_3code,
      ocr_3code,
      api_address,
      ocr_address,
      address_form,
      api_3code_match_error,
      api_3code_query_error,
      ocr_3code_match_error,
      ocr_3code_query_error,
      ocr_address_match_error,
      ocr_address_query_error,
      api_address_match_error,
      api_address_query_error,
      other_data,
    } = {},
  } = record;

  // 解析其他数据
  let other = {};
  try {
    other = JSON.parse(other_data) || {};
  } catch (error) {
    // 解析失败使用空对象
  }
  const { time = {} } = other;

  // 格式化时间信息，只展示不为0的时间
  const formatTimeInfo = (timeData, includeSpecialAndTotal = true) => {
    const timeItems = [];

    // 添加OCR拍图时间
    if (timeData.ocr_3code) {
      timeItems.push(`ocr拍图，耗时：${timeData.ocr_3code}毫秒`);
    }

    // 添加接口获取时间
    if (timeData.api_3code) {
      timeItems.push(`接口获取，耗时：${timeData.api_3code}毫秒`);
    }

    // 添加OCR地址时间
    if (timeData.ocr_address) {
      timeItems.push(`ocr地址，耗时：${timeData.ocr_address}毫秒`);
    }

    // 添加接口地址时间
    if (timeData.api_address) {
      timeItems.push(`接口地址，耗时：${timeData.api_address}毫秒`);
    }

    // 添加特殊件时间和总耗时
    if (includeSpecialAndTotal) {
      if (timeData.remind_type) {
        timeItems.push(`特殊件耗时：${timeData.remind_type}毫秒`);
      }

      if (timeData.time) {
        timeItems.push(`总耗时：${timeData.time}毫秒`);
      }
    }

    return timeItems.join('；');
  };

  // 数据映射
  const codeMap = {
    '3code': {
      ocr: {
        code: ocr_3code,
        error: ocr_3code_match_error
          ? `ocr拍图匹配段码失败：${ocr_3code_match_error}`
          : ocr_3code_query_error
            ? `ocr拍图获取段码失败：${ocr_3code_query_error}`
            : '',
        timeKey: 'ocr_3code',
      },
      third: {
        code: api_3code,
        error: api_3code_match_error
          ? `接口匹配段码失败：${api_3code_match_error}`
          : api_3code_query_error
            ? `接口获取段码失败：${api_3code_query_error}`
            : '',
        timeKey: 'api_3code',
      },
    },
    address: {
      ocr: {
        code: ocr_address,
        error: ocr_address_match_error
          ? `ocr拍图匹配地址失败：${ocr_address_match_error}`
          : ocr_address_query_error
            ? `ocr拍图获取地址失败：${ocr_address_query_error}`
            : '',
        timeKey: 'ocr_address',
      },
      third: {
        code: api_address,
        error: api_address_match_error
          ? `接口匹配地址失败：${api_address_match_error}`
          : api_address_query_error
            ? `接口获取地址失败：${api_address_query_error}`
            : '',
        timeKey: 'api_address',
      },
    },
  };

  // 默认值
  const result = {
    use: '',
    useDesc: '',
    unUse: '',
    unUseDesc: '',
    useTime: '',
    unUseTime: '',
  };

  // 主表单不存在，返回默认值
  if (!match_form || !codeMap[match_form]) {
    return result;
  }

  // 获取主表单类型对应的表单
  const form = match_form === '3code' ? three_code_form : address_form;
  if (!form || !codeMap[match_form][form]) {
    return result;
  }

  // 设置使用的数据
  const useData = codeMap[match_form][form];
  result.use = useData.code;
  result.useDesc = useData.error;
  result.useTime = formatTimeInfo(time, true);

  // 设置未使用的数据
  // 如果是段码表单，检查另一个段码来源
  if (match_form === '3code') {
    const otherForm = form === 'ocr' ? 'third' : 'ocr';
    if (codeMap[match_form][otherForm].code) {
      const unUseData = codeMap[match_form][otherForm];
      const { timeKey } = unUseData;
      result.unUse = unUseData.code;
      result.unUseDesc = unUseData.error;
      // 未使用的数据只显示对应时间，不显示特殊件和总耗时
      result.unUseTime = time[timeKey]
        ? `${form === 'ocr' ? '接口获取' : 'ocr拍图'}，耗时：${time[timeKey]}毫秒`
        : '';
    } else if (codeMap.address[address_form]) {
      // 如果另一个段码来源没有数据，则使用地址数据
      const unUseData = codeMap.address[address_form];
      const { timeKey } = unUseData;
      result.unUse = unUseData.code;
      result.unUseDesc = unUseData.error;
      // 未使用的地址数据只显示对应时间，不显示特殊件
      result.unUseTime = time[timeKey]
        ? `${address_form === 'ocr' ? 'ocr地址' : '接口地址'}，耗时：${time[timeKey]}毫秒`
        : '';
    }
  } else if (match_form === 'address') {
    // 如果是地址表单，检查另一个地址来源
    const otherForm = form === 'ocr' ? 'third' : 'ocr';
    if (codeMap[match_form][otherForm].code) {
      const unUseData = codeMap[match_form][otherForm];
      const { timeKey } = unUseData;
      result.unUse = unUseData.code;
      result.unUseDesc = unUseData.error;
      // 未使用的数据只显示对应时间，不显示特殊件和总耗时
      result.unUseTime = time[timeKey]
        ? `${form === 'ocr' ? '接口地址' : 'ocr地址'}，耗时：${time[timeKey]}毫秒`
        : '';
    }
  }

  return result;
};

export const patchIndex = data => {
  if (!data) return {};

  // 处理数组类型数据，为每个元素添加_index
  if (Array.isArray(data)) {
    return data.map((item, index) => ({
      ...item,
      _index: index,
    }));
  }

  // 处理对象类型数据
  if (isPlainObject(data)) {
    // 创建新对象以保存结果
    const result = { ...data };
    // 使用Object.keys循环遍历对象的所有属性
    Object.keys(data).forEach(key => {
      const value = data[key];
      // 如果属性值是数组，为每个元素添加_index
      if (Array.isArray(value)) {
        result[key] = value.map((item, index) => ({
          ...item,
          _index: index,
        }));
      }
    });
    return result;
  }

  // 返回原始数据（非对象非数组的情况）
  return data;
};
