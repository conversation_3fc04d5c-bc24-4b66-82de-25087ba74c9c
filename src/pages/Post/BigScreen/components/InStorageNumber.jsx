/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

/**
 * 每日入库量
 *  */
import React, { useState, useCallback, useRef, useEffect, useMemo } from 'react';
import { connect } from 'dva';
import { useInterval } from 'ahooks';
import { Chart, Geom, Axis, Tooltip, Label } from 'bizcharts';
import PropTypes from 'prop-types';
import moment from 'moment';
import { lineStyleColumn, gradualTextColumn } from './_utils';
import CommonBorder from './Wrappers/CommonBorder';
import NoDate from './Wrappers/NoDate';

const cols = {
  num: {
    alias: '数量',
  },
  date: {
    type: 'cat',
  },
};

const label = {
  formatter: val => (val >= 10000 ? `${Math.round(val / 10000)}万` : val),
  textStyle: {
    fill: gradualTextColumn, // 文本的颜色
    fontSize: '14', // 文本大小
    fontWeight: 'bold', // 文本粗细
  },
};

const map = type => {
  switch (type) {
    case 'in':
      // 调整为折线图 StationNum
      return {
        url: 'getInStorageNum',
        title: '每日入库量',
      };
    case 'out':
      return {
        url: 'getOutStorageNum',
        title: '每日寄件量',
      };
    case 'fans':
      return {
        url: 'getFansNum',
        title: '每日新增粉丝数',
      };
    case 'station':
      // 调整为折线图 StationNum
      return {
        url: 'getStageNum',
        title: '每日新增站点数',
      };
    default:
      return {
        url: 'getInStorageNum',
        title: '每日入库量',
      };
  }
};

const InStorageNumber = ({ dispatch, isParentFull, loading, type = 'in', token }) => {
  const [data, setData] = useState({});
  const [hasData, setHasData] = useState(false);
  const [height, setHeight] = useState(0);
  const heightRef = useRef({});
  const timer = useRef(null);

  const fetchData = useCallback(
    () => {
      dispatch({
        type: `chartData/${map(type).url}`,
        payload: {
          token,
        },
      }).then((res = []) => {
        setHasData(res.length > 0);
        setData(res);
      });
    },
    [dispatch, token, type],
  );

  const title = useMemo(() => map(type).title, [type]);

  useEffect(
    () => {
      timer.current = setTimeout(() => {
        heightRef.current && setHeight(heightRef.current.clientHeight);
      }, 1000);
      return () => {
        timer.current && clearTimeout(timer.current);
      };
    },
    [isParentFull],
  );

  useEffect(fetchData, [fetchData]);
  useInterval(fetchData, 1000 * 60 * 60);

  return (
    <CommonBorder title={title} loading={loading}>
      <div style={{ height: '100%' }} ref={heightRef}>
        {hasData ? (
          <Chart
            style={{ backgroundColor: 'rgba(15, 24, 55, 0.7)' }}
            height={height}
            data={data}
            forceFit
            scale={cols}
            padding={[50, 20, 'auto', 'auto']}
          >
            <Axis
              name="date"
              label={{
                ...label,
                formatter: val => moment(val).format('DD'),
              }}
              tickLine={null}
            />
            <Axis name="num" label={label} grid={null} />
            <Tooltip
              crosshairs={{
                type: 'y',
              }}
            />
            <Geom type="interval" position="date*num" shape="round-rect" style={lineStyleColumn}>
              <Label content="num" textStyle={label.textStyle} textAlign="center" />
            </Geom>
          </Chart>
        ) : (
          <NoDate title="暂无数据" style={{ height }} />
        )}
      </div>
    </CommonBorder>
  );
};

InStorageNumber.propTypes = {
  // type: PropTypes.oneOf(["in", "send"]).isRequired,
  type: PropTypes.string.isRequired,
};

export default connect(({ loading, global, setting }, { type = 'in' }) => ({
  token: global.screenToken,
  loading: loading.effects[`chartData/${map(type).url}`],
  options: setting.options,
}))(InStorageNumber);
