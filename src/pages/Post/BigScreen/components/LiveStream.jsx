/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
/* eslint-disable no-console */

import React, { useState, useCallback, useRef, useMemo, useEffect } from 'react';
import { useUpdateEffect, useUnmount, usePrevious } from 'ahooks';
import { connect } from 'dva';
import moment from 'moment';
import { Row, Col, message, Spin } from 'antd';
import SwiperCore, { Navigation } from 'swiper';
import classnames from 'classnames';
import zoom_in from '@/assets/bigScreen/zoom_in.png';
import zoom_out from '@/assets/bigScreen/zoom_out.png';
import ReactPlayer from 'react-player';
import { sliceArrByNum } from '@/utils/utils';
import CommonBorder from './Wrappers/CommonBorder';
import styles from './LiveStream.less';
import VideoSwiper from '@/components/_pages/video/components/videoSwiper';
import Tabs from '@/components/_pages/video/components/tabs';

SwiperCore.use([Navigation]);

export const NoData = ({ text }) => (
  <Row className={styles.nodata} type="flex" justify="center" align="middle">
    <Col>{text}</Col>
  </Row>
);

const LiveStream = ({
  dispatch,
  currentBranchId,
  isParentFull,
  screenToken,
  currentLevel,
  cm_ids,
  toggleFull,
  onlyInn,
  isYz,
}) => {
  const [url, setUrl] = useState('');
  const [playing, setplaying] = useState(false);
  const [deviceName, setDeviceName] = useState(null);
  const [cmName, setCmName] = useState(null);
  const [channelLists, setChannelList] = useState([]);
  const [currentChannel, setCurrentChannel] = useState({});
  const [currentVideo, setCurrentVideo] = useState({});
  const [full, setFull] = useState(false);
  const [pause, setPause] = useState(false);
  const [liveId, setLiveId] = useState('');
  const [timerCount, setCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [errorInfo, setErrorInfo] = useState('');
  const timer = useRef(null);
  const totalCount = useRef(0);

  const prevLevel = usePrevious(currentLevel);

  // 获取驿站id
  const getYzIds = useCallback(
    async branch_id => {
      const cmids = await dispatch({
        type: 'chartData/getSubDaksList',
        payload: {
          token: screenToken,
          level: currentLevel,
          branch_id: branch_id == 0 ? 336 : branch_id, //  全国默认使用杭州市的数据
        },
      });
      return cmids.map(i => i.cm_id).join(',');
    },
    [dispatch, screenToken, currentLevel],
  );

  // 获取有视频驿站ID
  const getCmidEnableVideo = useCallback(
    async (ids = '') => {
      const reqParams = ids.split(',');
      const queen = sliceArrByNum(reqParams, 200);
      const res = [];

      for (let i = 0; i < queen.length; i += 1) {
        const cmid = queen[i];
        // eslint-disable-next-line no-await-in-loop
        const data = await dispatch({
          type: 'chartData/getHasVideoCmId',
          payload: {
            cm_ids: cmid.join(','),
            token: screenToken,
            share_type: cm_ids ? 'video' : 'statistic',
          },
        });
        if (data) {
          res.push(data);
        }
      }
      return res.join(',');
    },
    [dispatch, screenToken, cm_ids],
  );

  // 获取驿站通道
  const getChannelList = useCallback(
    ids =>
      dispatch({
        type: 'chartData/getCityChannelList',
        payload: {
          token: screenToken,
          cm_ids: ids,
          share_type: cm_ids ? 'video' : 'statistic',
        },
      }),
    [dispatch, screenToken, cm_ids],
  );

  // 获取播放地址
  const getVideoURL = useCallback(
    (channelData = {}) => {
      setCurrentVideo(channelData);
      return dispatch({
        type: 'chartData/getVideoURL',
        payload: {
          channel_no: channelData.channelNo,
          device_serial: channelData.deviceSerial,
          stream_index: 0,
          stream_type: 'live',
          token: screenToken,
          share_type: cm_ids ? 'video' : 'statistic',
        },
      });
    },
    [dispatch, screenToken, cm_ids],
  );

  // 推流播放
  const palyVideo = useCallback(
    vurl => {
      setLoading(true);
      dispatch({
        type: 'chartData/startLive',
        payload: {
          video_url: vurl,
          token: screenToken,
          share_type: cm_ids ? 'video' : 'statistic',
        },
      }).then(_liveId => {
        setLiveId(_liveId);
        setUrl(vurl.replace('http:', 'https:'));
        setplaying(true);
        setPause(false);
        // setLoading(false);
      });
    },
    [dispatch, screenToken, full, cm_ids],
  );

  // 关闭推流
  const closeStream = useCallback(
    unmount => {
      if (liveId) {
        setLoading(false);
        clearTimeout(timer.current);
        setCount(0);
        dispatch({
          type: 'chartData/stopLive',
          payload: {
            live_id: liveId,
            token: screenToken,
            share_type: cm_ids ? 'video' : 'statistic',
          },
        })
          .then(() => {
            if (!unmount) {
              setplaying(false);
              setUrl(null);
              setLiveId(null);
            }
          })
          .catch(err => {
            console('stop_video_err', err);
          });
      }
    },
    [liveId, dispatch, screenToken, cm_ids],
  );

  // 获取当前层级的通道号
  const getChannelListReady = useCallback(
    async branch_id => {
      let _channelLists;
      if (!cm_ids) {
        const init_cm_ids = (await getYzIds(branch_id)) || '';
        const enable_cmids = (await getCmidEnableVideo(init_cm_ids)) || '';
        if (!enable_cmids) return;
        _channelLists = (await getChannelList(enable_cmids)) || {};
      } else {
        _channelLists = (await getChannelList(cm_ids)) || {};
      }

      setChannelList(_channelLists);
      // eslint-disable-next-line consistent-return
      return _channelLists;
    },
    [getYzIds, getCmidEnableVideo, getChannelList, cm_ids],
  );

  const onError = useCallback(
    error => {
      console.log('error', error, moment().format('llll'));
      setUrl(null);
      // 播放失败重试
      if (timerCount >= 19) {
        setCount(0);
        totalCount.current += 1;
        // 超时停止播放
        if (totalCount.current >= 2) {
          totalCount.current = 0;
          setErrorInfo('加载失败');
          setUrl(null);
          closeStream();
          setCurrentVideo({});
          return;
        }
        setErrorInfo('加载失败，正在重试...');
        getVideoURL(currentVideo).then(vurl => {
          palyVideo(vurl);
        });
        return;
      }

      timer.current = setTimeout(() => {
        setUrl(url);
        setCount(timerCount + 1);
      }, 1000);
    },
    [timerCount, closeStream, palyVideo, getVideoURL, url, currentVideo],
  );

  const onReady = useCallback(() => {
    setplaying(true);
    setCount(0);
    timer.current && clearTimeout(timer.current);
  }, []);

  const onPause = useCallback(() => {
    console.log('暂停');
    setLoading(false);
  }, []);

  const onStart = useCallback(() => {
    console.log('播放');
    setLoading(false);
    timer.current && clearTimeout(timer.current);
  }, []);

  const onVideoClick = useCallback(
    () => {
      if (!cm_ids && !isYz) return;
      if (!isParentFull) {
        if (!toggleFull) {
          message.warn('请在全屏下查看');
          return;
        }
        !onlyInn && toggleFull();
        setTimeout(() => {
          setFull(true);
        }, 300);
      }
      if (!full) {
        setFull(true);
      }
    },
    [full, isParentFull, cm_ids],
  );

  // 选择机位
  const onChangeChannel = useCallback(
    () => {
      if (cm_ids) return;
      setplaying(!playing);
      setPause(!pause);
    },
    [playing, pause, cm_ids],
  );

  // 切换机位
  const onChannelChange = useCallback(
    channelData => {
      closeStream();
      setDeviceName(channelData.channelName);
      getVideoURL(channelData).then(vurl => {
        palyVideo(vurl);
      });
    },
    [getVideoURL, palyVideo, closeStream],
  );

  // 切换驿站
  const onInnVideoChange = useCallback(
    ({ cm_id, cm_name, channelList, ...rest }) => {
      closeStream();
      if (cm_id) {
        const currentChannelData = channelList[0] || {};
        const currentChannelName = currentChannelData.channelName;

        setDeviceName(currentChannelName);
        setCmName(cm_name);
        setCurrentChannel({ channelList, ...rest });
        getVideoURL(currentChannelData).then(vurl => {
          palyVideo(vurl);
        });
      }
    },
    [getVideoURL, palyVideo, closeStream],
  );

  // 视频退出全屏
  const onZoomIn = () => {
    setFull(false);
    setPause(false);
    toggleFull?.();
  };

  // 播放视频
  useEffect(
    () => {
      if ((currentLevel != prevLevel && currentLevel) || cm_ids || isYz) {
        closeStream();
        getChannelListReady(currentBranchId).then(_channelLists => {
          const { channelList = [], ...rest } = _channelLists[0] || {};
          const currentChannelData = channelList[0] || {};
          const currentChannelName = currentChannelData.channelName || null;
          const currentCmlName = rest.cm_name || null;

          if (!channelList?.length) {
            setDeviceName(null);
            setCmName(null);
            setCurrentChannel({});
            return;
          }

          setDeviceName(currentChannelName);
          setCmName(currentCmlName);
          setCurrentChannel({ channelList, ...rest });
          getVideoURL(currentChannelData).then(vurl => {
            palyVideo(vurl);
          });
        });
      }
    },
    [getChannelListReady],
  );

  // 卸载关闭视频
  useUnmount(() => {
    closeStream(true);
  });

  useUpdateEffect(
    () => {
      // 当大屏退出全屏时，视频自动退出全屏
      if (!isParentFull && full) {
        setFull(false);
      }
    },
    [isParentFull, full],
  );

  const isChannel = useMemo(() => cm_ids || (pause && !cm_ids), [cm_ids, pause]);

  return (
    <CommonBorder title="实时监控" loading={loading && !(full || onlyInn)} hideTitle={!!cm_ids}>
      <div
        className={classnames(styles['player-wrapper'], {
          [styles.full]: full || onlyInn,
          [styles.full_bg_color]: full || onlyInn,
        })}
        style={{
          paddingTop: full || onlyInn ? '' : '56.25%',
          height: '100%',
        }} /* Player ratio: 100 / (1280 / 720) */
        onClick={onVideoClick}
      >
        {(full || onlyInn) && (
          <Spin spinning={loading}>
            <div className={`${styles.selectArea} liveFullWrap`}>
              <Row
                type="flex"
                align="middle"
                justify="space-between"
                className={styles.full_header}
              >
                <Col style={{ marginLeft: 50 }}>
                  <Tabs
                    cmName={cmName}
                    deviceName={deviceName}
                    currentChannel={currentChannel}
                    onChangeChannel={onChangeChannel}
                  />
                </Col>
                <Col className={styles.zoom_in}>
                  {onlyInn ? (
                    <img src={isParentFull ? zoom_in : zoom_out} alt="" onClick={toggleFull} />
                  ) : (
                    <img onClick={onZoomIn} src={zoom_in} alt="缩小" />
                  )}
                </Col>
              </Row>
              <VideoSwiper
                isChannel={isChannel}
                list={isChannel ? currentChannel.channelList : channelLists}
                onClick={isChannel ? onChannelChange : onInnVideoChange}
              />
            </div>
          </Spin>
        )}
        {!playing && !loading && <NoData className={styles.failed} text={errorInfo} />}
        <ReactPlayer
          style={{ cursor: !full ? 'pointer' : 'auto' }}
          className={styles['react-player']}
          width="100%"
          height="100%"
          muted
          url={url}
          playing={playing}
          onStart={onStart}
          onPause={onPause}
          onError={onError}
          onReady={onReady}
        />
      </div>
    </CommonBorder>
  );
};

export default connect(({ chartData, global }) => ({
  screenToken: global.screenToken,
  liveId: chartData.liveId,
  currentBranchId: chartData.currentAreaInfo.branchId,
  currentUserLevel: chartData.currentAreaInfo.currentUserLevel,
  currentLevel: chartData.currentAreaInfo.currentLevel,
}))(LiveStream);
