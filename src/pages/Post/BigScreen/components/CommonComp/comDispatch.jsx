/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import React from 'react';
import OutRateRange from '../OutRateRange';
import InStorageNumber from '../InStorageNumber';
import OutRate from '../OutRate';
import YzYesterdayInNum from '../YzYesterdayInNum';
import YzBrandsRate from '../YzBrandsRate';
import StationNum from '../StationNum';
import LiveStream from '../LiveStream';

const ComDispatch = props => {
  const { num: index, configKey, config, ...rest } = props;

  const num = config[configKey][index];
  const { showCabinet } = config;

  return num == 3 ? (
    <InStorageNumber type="station" {...rest} />
  ) : num == 2 ? (
    <OutRate {...rest} />
  ) : num == 5 || num == 1 || num == 4 ? (
    <StationNum
      type={num == 1 ? 'in' : num == 4 ? 'new_station' : 'station'}
      showCabinet={showCabinet}
      {...rest}
    />
  ) : num == 6 ? (
    <OutRateRange {...rest} />
  ) : num == 7 ? (
    <YzBrandsRate title="昨日入库品牌占比" isPost={false} {...rest} />
  ) : num == 8 ? (
    <YzYesterdayInNum {...rest} />
  ) : num == 9 ? (
    <LiveStream isYz {...rest} />
  ) : null;
};

export default ComDispatch;
