/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import { setStorageSync, getStorageSync } from '@/utils/storage';
import { Button, Checkbox, Col, Dropdown, Menu, message, Row } from 'antd';
import React, { useEffect, useState } from 'react';
import { defaultTodayConfig, todayList } from './configs/todayData';

const list = [
  { key: 1, title: '每日入库量' },
  { key: 2, title: '3日出库率' },
  { key: 3, title: '每日寄件量' },
  { key: 4, title: '每日新增站点数' },
  { key: 5, title: '驿站数量' },
  { key: 6, title: '3日出库排名' },
  { key: 7, title: '昨日入库品牌占比' },
  { key: 8, title: '昨日入库量排名' },
  { key: 9, title: '驿站监控视频' },
];

export const dashboardSettingKey = 'kb-dashboardSetting';

// 兼容历史数据的函数，将旧格式转换为新格式
const compatibleHistoryData = data => {
  // 如果是新格式，直接返回
  if (
    data &&
    typeof data === 'object' &&
    !Array.isArray(data) &&
    (data.aside || data.today || data.showCabinet !== undefined)
  ) {
    return {
      aside: data.aside || [],
      today: data.today || [],
      showCabinet: data.showCabinet || false,
    };
  }

  // 如果是旧格式（数组），转换为新格式
  if (Array.isArray(data)) {
    return {
      aside: data,
      today: defaultTodayConfig,
      showCabinet: false,
    };
  }

  // 默认值
  return {
    aside: [1, 2, 3, 6, 7, 8],
    today: defaultTodayConfig,
    showCabinet: false,
  };
};

const SettingDispatch = ({ onChange }) => {
  const [checkedData, setCheckedData] = useState({ aside: [], today: [], showCabinet: false });
  const [originalValue, setOriginalValue] = useState({
    aside: [],
    today: [],
    showCabinet: false,
  }); // 保存原始值用于取消时重置
  const [visible, setVisible] = useState(false);
  const [initialized, setInitialized] = useState(false); // 标记是否已初始化

  const handleClick = (key, type) => {
    if (type === 'aside') {
      const newAside = checkedData.aside.includes(key)
        ? checkedData.aside.filter(item => item !== key)
        : [...checkedData.aside, key];
      setCheckedData({ ...checkedData, aside: newAside });
    } else if (type === 'today') {
      const newToday = checkedData.today.includes(key)
        ? checkedData.today.filter(item => item !== key)
        : [...checkedData.today, key];
      setCheckedData({ ...checkedData, today: newToday });
    }
  };

  const handleCabinetToggle = () => {
    setCheckedData(prev => ({ ...prev, showCabinet: !prev.showCabinet }));
  };

  const onSubmit = () => {
    if (checkedData.aside.length < 6) {
      message.warning('两侧数据请至少选择6项');
      return;
    }
    if (checkedData.today.length < 4) {
      message.warning('今日数据请至少选择4项');
      return;
    }
    setVisible(false);
    const newData = {
      aside: checkedData.aside.sort((a, b) => a - b),
      today: checkedData.today.sort((a, b) => a - b),
      showCabinet: checkedData.showCabinet,
    };
    setStorageSync(dashboardSettingKey, newData);
    onChange(newData);
    setOriginalValue(newData); // 更新原始值
  };

  const onCancel = () => {
    setVisible(false);
    setCheckedData(originalValue); // 重置为原始值
  };

  const onOpen = () => {
    setVisible(true);
  };

  // 初始化时处理历史数据
  useEffect(
    () => {
      if (!initialized) {
        const { data } = getStorageSync(dashboardSettingKey) || {};
        const compatibleValue = compatibleHistoryData(data);

        // 如果是从旧格式转换的，需要写入新格式到storage
        if (Array.isArray(data)) {
          setStorageSync(dashboardSettingKey, compatibleValue);
        }

        setCheckedData(compatibleValue);
        setOriginalValue(compatibleValue);
        onChange(compatibleValue);
        setInitialized(true);
      }
    },
    [initialized],
  );

  return (
    <Dropdown
      trigger={['click']}
      placement="bottomRight"
      visible={visible}
      overlay={
        <div style={{ backgroundColor: 'white', padding: 12, width: 400 }}>
          <div style={{ paddingBottom: 12 }}>
            <Checkbox checked={checkedData.showCabinet} onClick={handleCabinetToggle}>
              单独显示快递柜数据
            </Checkbox>
          </div>
          <Row type="flex" justify="space-between">
            <Col span={12}>
              <div style={{ paddingLeft: 12 }}>两侧数据</div>
              <Menu>
                {list.map(item => (
                  <Menu.Item key={item.key}>
                    <Checkbox
                      checked={checkedData.aside.includes(item.key)}
                      onClick={() => handleClick(item.key, 'aside')}
                    >
                      {item.title}
                    </Checkbox>
                  </Menu.Item>
                ))}
              </Menu>
            </Col>
            <Col span={12}>
              <div style={{ paddingLeft: 12 }}>今日数据</div>
              <Menu>
                {todayList.map(item => (
                  <Menu.Item key={item.key}>
                    <Checkbox
                      checked={checkedData.today.includes(item.key)}
                      onClick={() => handleClick(item.key, 'today')}
                    >
                      {item.title}
                    </Checkbox>
                  </Menu.Item>
                ))}
              </Menu>
            </Col>
          </Row>
          <Row type="flex" justify="center" gutter={12} style={{ marginTop: 16 }}>
            <Col>
              <Button onClick={onCancel}>取消</Button>
            </Col>
            <Col>
              <Button
                type="primary"
                onClick={onSubmit}
                disabled={
                  checkedData.aside.length < 6 ||
                  (checkedData.today.length > 0 && checkedData.today.length < 4)
                }
              >
                确定
              </Button>
            </Col>
          </Row>
        </div>
      }
    >
      <Button size="small" icon="setting" onClick={onOpen} />
    </Dropdown>
  );
};

export default SettingDispatch;
