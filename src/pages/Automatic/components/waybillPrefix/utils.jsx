/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import React, { useEffect } from 'react';
import { Modal } from 'antd';
import { useMemo, useState } from 'react';
import { Form } from 'antd4';

export const useWaybillPrefix = props => {
  const { value: waybillPrefix, onChange, brand, brands } = props;
  const [visible, setVisible] = useState(false);
  const [form] = Form.useForm();

  const handleOk = async () => {
    const values = await form.validateFields();
    const waybillPrefixValues = values.waybillPrefix || [];
    onChange(waybillPrefixValues);
    setVisible(false);
  };

  const handleClear = () => {
    onChange([]);
  };

  const selectValue = useMemo(
    () => {
      return Array.isArray(waybillPrefix) && waybillPrefix.length
        ? waybillPrefix.join('/')
        : undefined;
    },
    [waybillPrefix],
  );

  const brandName = useMemo(
    () => {
      return brands.find(item => item.brand == brand)?.name ?? brand;
    },
    [brand, brands],
  );

  useEffect(
    () => {
      if (visible) {
        const initialValues =
          Array.isArray(waybillPrefix) && waybillPrefix.length ? waybillPrefix : [''];
        form.setFieldsValue({
          waybillPrefix: initialValues,
        });
      }
    },
    [visible, waybillPrefix, form],
  );

  return {
    visible,
    setVisible,
    showTips,
    selectValue,
    form,
    handleOk,
    brandName,
    handleClear,
  };
};

const showTips = () => {
  Modal.info({
    title: '温馨提示',
    content: (
      <div>
        <div>针对格口可设置品牌单号前缀落格，同品牌单号最多可匹配单号前四位</div>
        <div>如：</div>
        <div>申通设置单号前2位65开头落格（申通分拣菜鸟单号）</div>
        <div>邮政设置单号前2位98开头落格（邮政电商）</div>
        <div>匹配优先级：</div>
        <div>优先匹配单号前缀，后匹配段码或地址</div>
      </div>
    ),
  });
};

const keys = ['waybillPrefix'];
export const addExtendData = (extendData, data) => {
  let extend_data = {};
  try {
    extend_data = JSON.parse(extendData);
  } catch (error) {
    extend_data = {};
  }
  const payload = { ...data };
  keys.forEach(key => {
    if (Object.prototype.hasOwnProperty.call(payload, key)) {
      payload.extend_data = JSON.stringify({
        ...extend_data,
        [key]: payload[key],
      });
      delete payload[key];
    }
  });
  return payload;
};

export const renderExtraData = (extend_data, key) => {
  let data = {};
  try {
    data = JSON.parse(extend_data);
  } catch (error) {
    return '';
  }
  return data[key]?.join('/') || '';
};
