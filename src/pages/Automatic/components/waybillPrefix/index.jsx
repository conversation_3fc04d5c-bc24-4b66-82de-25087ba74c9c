/* eslint-disable react/no-array-index-key */
/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import React, { forwardRef } from 'react';
import { MinusOutlined, PlusOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { message, Select } from 'antd';
import { Form, Input, Modal, Button } from 'antd4';
import { useWaybillPrefix } from './utils';
import Antd4Provider from '@/components/antd4/antd4provider';

const WaybillPrefix = forwardRef((props, ref) => {
  const {
    visible,
    setVisible,
    showTips,
    selectValue,
    form,
    brandName,
    handleOk,
    handleClear,
  } = useWaybillPrefix(props, ref);

  return (
    <Antd4Provider>
      <div onClick={() => setVisible(true)}>
        <Select
          value={selectValue}
          open={false}
          allowClear
          onChange={handleClear}
          placeholder="按单号前缀不同数字进行分拣"
        />
      </div>
      <Modal
        title={
          <span onClick={showTips} style={{ cursor: 'pointer' }}>
            单号前缀分拣
            <QuestionCircleOutlined />
          </span>
        }
        open={visible}
        destroyOnClose
        onCancel={() => setVisible(false)}
        onOk={handleOk}
      >
        <Form form={form} labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
          <Form.List name="waybillPrefix">
            {(fields, { add, remove }) => (
              <>
                {fields.map((field, index) => (
                  <Form.Item
                    {...field}
                    key={field.key}
                    label={`${brandName}单号前缀${fields.length > 1 ? `${index + 1}` : ''}`}
                    name={[field.name]}
                    rules={[
                      { required: true, message: '请输入单号前缀的开头字符' },
                      {
                        max: 15,
                        message: '最多支持输入15位字符',
                      },
                    ]}
                  >
                    <Input
                      maxLength={15}
                      showCount
                      allowClear
                      placeholder="请输入单号前缀的开头字符"
                      addonAfter={
                        index === 0 ? (
                          <Button
                            type="primary"
                            shape="circle"
                            icon={<PlusOutlined />}
                            onClick={() => {
                              if (fields.length == 10) {
                                message.error('最多可设置10个单号前缀');
                                return;
                              }
                              add();
                            }}
                            size="small"
                          />
                        ) : (
                          <Button
                            type="default"
                            shape="circle"
                            icon={<MinusOutlined />}
                            onClick={() => remove(field.name)}
                            size="small"
                          />
                        )
                      }
                    />
                  </Form.Item>
                ))}
              </>
            )}
          </Form.List>
        </Form>
      </Modal>
    </Antd4Provider>
  );
});

export default WaybillPrefix;
