/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { mockPhone, mockResponse } from './_utils';

export default {
  // 获取巴枪扫描配置列表
  'POST /v1/TotalDistribution/ExpressCompanyInfo/queryCompanyInfo': (req, res) => {
    res.send({
      code: 0,
      msg: '请求成功/Api/allocation/getPostList',
      data: [
        {
          id: '2',
          kb_id: '3355165',
          previous_station_code: '上一站编号',
          next_station_code: '下一站编号',
          brand: 'zt',
          branch_code: '*********',
          branch_name: '中通网点名称',
          previous_station_name: '上一站名称',
          user_name: '用户名',
          user_pwd: '密码',
          arrival_gun_code: '到件巴枪设备编号',
          delivery_gun_code: '派件巴枪扫描',
          disabled: '1',
          scan_type: '3,4',
          update_time: '2019-03-07 11:07:25',
          create_time: '2019-03-04 14:54:50',
        },
        {
          id: '3',
          kb_id: '3355165',
          previous_station_code: '336562',
          next_station_code: '',
          brand: 'sto',
          branch_code: '98987',
          branch_name: '明ss',
          previous_station_name: '上海虹桥火车站网点',
          user_name: '15221553557',
          user_pwd: '*********',
          arrival_gun_code: '67251622139952',
          delivery_gun_code: '67251611043311',
          disabled: '0',
          scan_type: '1,3,4',
          update_time: '2019-03-07 11:07:25',
          create_time: '2019-03-04 14:54:50',
        },
        {
          id: '4',
          kb_id: '3355165',
          previous_station_code: '336562',
          next_station_code: '',
          brand: 'yt',
          branch_code: '98987',
          branch_name: '明ss',
          previous_station_name: '上海虹桥火车站网点',
          user_name: '15221553557',
          user_pwd: '*********',
          arrival_gun_code: '67251622139952',
          delivery_gun_code: '67251611043311',
          disabled: '1',
          scan_type: '1,2,3,4,0',
          update_time: '2019-03-07 11:07:25',
          create_time: '2019-03-04 14:54:50',
          dev_id: '12312',
          dev_imei: '3321',
        },
        {
          id: '5',
          kb_id: '3355165',
          previous_station_code: '336562',
          next_station_code: '',
          brand: 'ht',
          branch_code: '98987',
          branch_name: '明ss',
          previous_station_name: '上海虹桥火车站网点',
          user_name: '15221553557',
          user_pwd: '*********',
          arrival_gun_code: '67251622139952',
          delivery_gun_code: '67251611043311',
          disabled: '0',
          scan_type: '1,2,3,4',
          update_time: '2019-03-07 11:07:25',
          create_time: '2019-03-04 14:54:50',
        },
        {
          id: '6',
          kb_id: '3355165',
          previous_station_code: '336562',
          next_station_code: '',
          brand: 'tt',
          branch_code: '98987',
          branch_name: '明ss',
          previous_station_name: '上海虹桥火车站网点',
          user_name: '15221553557',
          user_pwd: '*********',
          arrival_gun_code: '67251622139952',
          delivery_gun_code: '67251611043311',
          disabled: '0',
          scan_type: '1,2,3,4',
          update_time: '2019-03-07 11:07:25',
          create_time: '2019-03-04 14:54:50',
        },
        {
          id: '7',
          kb_id: '3355165',
          previous_station_code: '336562',
          next_station_code: '',
          brand: 'jt',
          branch_code: '98987',
          branch_name: '明ss',
          previous_station_name: '上海虹桥火车站网点',
          user_name: '15221553557',
          user_pwd: '*********',
          arrival_gun_code: '67251622139952',
          delivery_gun_code: '67251611043311',
          disabled: '0',
          scan_type: '1,2,3,4',
          update_time: '2019-03-07 11:07:25',
          create_time: '2019-03-04 14:54:50',
        },
        {
          id: '8',
          kb_id: '3355165',
          previous_station_code: '336562',
          next_station_code: '',
          brand: 'yd',
          branch_code: '98987',
          branch_name: '明ss',
          previous_station_name: '上海虹桥火车站网点',
          user_name: '15221553557',
          user_pwd: '*********',
          arrival_gun_code: '67251622139952',
          delivery_gun_code: '67251611043311',
          disabled: '0',
          scan_type: '1,2,3,4',
          update_time: '2019-03-07 11:07:25',
          create_time: '2019-03-04 14:54:50',
        },
        {
          id: '9',
          kb_id: '3355165',
          previous_station_code: '336562',
          next_station_code: '',
          brand: 'zykd',
          branch_code: '98987',
          branch_name: '明ss',
          previous_station_name: '上海虹桥火车站网点',
          user_name: '15221553557',
          user_pwd: '*********',
          arrival_gun_code: '67251622139952',
          delivery_gun_code: '67251611043311',
          disabled: '0',
          scan_type: '1,2,3,4',
          update_time: '2019-03-07 11:07:25',
          create_time: '2019-03-04 14:54:50',
        },
        {
          id: '10',
          kb_id: '3355165',
          previous_station_code: '336562',
          next_station_code: '',
          brand: 'ems',
          branch_code: '98987',
          branch_name: '明ss',
          previous_station_name: '上海虹桥火车站网点',
          user_name: '15221553557',
          user_pwd: '*********',
          arrival_gun_code: '67251622139952',
          delivery_gun_code: '67251611043311',
          disabled: '0',
          scan_type: '1,2,3,4',
          update_time: '2019-03-07 11:07:25',
          create_time: '2019-03-04 14:54:50',
        },
        {
          id: '11',
          kb_id: '3355165',
          previous_station_code: '336562',
          next_station_code: '',
          brand: 'fw',
          branch_code: '98987',
          branch_name: '明ss',
          previous_station_name: '上海虹桥火车站网点',
          user_name: '15221553557',
          user_pwd: '*********',
          arrival_gun_code: '67251622139952',
          delivery_gun_code: '67251611043311',
          disabled: '0',
          scan_type: '1,2,3,4',
          update_time: '2019-03-07 11:07:25',
          create_time: '2019-03-04 14:54:50',
        },
      ],
    });
  },
  // 删除巴枪扫描配置列表
  'POST /v1/TotalDistribution/ExpressCompanyInfo/deleteCompanyInfo': (req, res) => {
    res.send({
      code: 0,
      msg: '请求成功deleteOption',
      data: {},
    });
  },
  // 开启/关闭服务
  'POST /v1/TotalDistribution/ExpressCompanyInfo/disabledCompanyInfo': (req, res) => {
    setTimeout(() => {
      res.send({
        code: 0,
        msg: '请求成功openService',
        data: {},
      });
    }, 500);
  },
  // 获取单条快递公司信息，//巴枪设备编号，添加、删除编号
  'POST /v1/TotalDistribution/ExpressCompanyInfo/scanConfig': (req, res) => {
    res.send({
      code: 0,
      msg: '请求成功',
      data: {
        res: 'success',
      },
    });
  },
  // 新增快递公司信息
  'POST /v1/TotalDistribution/ExpressCompanyInfo/addCompanyInfo': (req, res) => {
    res.send({
      code: 0,
      msg: '请求成功addOption',
      data: {},
    });
  },
  // 修改快递公司信息
  'POST /v1/TotalDistribution/ExpressCompanyInfo/amendCompanyInfo': (req, res) => {
    res.send({
      code: 0,
      msg: '请求成功addOption',
      data: {},
    });
  },
  // 获得到件扫描记录的搜索列表
  'POST /v1/TotalDistribution/MultiBrandQueryInfo/getScanRecord': (req, res) =>
    mockResponse(
      {
        cm_name: '@cname',
        branch_no: '@id',
        waybill_no: /\d{8,20}/,
        cm_phone: mockPhone(),
        'send_status|1': ['1', '2'],
        fail_desc: '@cword(5,10)',
        scan_time: '@datetime',
        create_time: '@datetime',
        update_time: '@datetime',
        brand: req.body.brand || 'yt',
        next_station: '123',
        next_station_name: '下一站',
        bad_waybill_type: '@cword(5,10)',
        bad_waybill_desc: '@cword(5,10)',
        bad_waybill_describe: '@cword(5,10)',
        sign_type: '签收人',
        '3rd_code': 'third_code',
        sign_pic: '@image',
        'sign_pic_multi|2-5': ['@image'],
        scan_type: '@cword(3,9)',
      },
      {
        _key: 'list',
        ct: 50,
      },
    )(req, res),
  // 获得网点信息
  'POST /v1/TotalDistribution/ExpressCompanyInfo/getBranchInfo': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {
        ems: ['011500-深泽', '31002112-彭埠营业部'],
        fw: [
          'ceshi001-测试网点丰网',
          'cs006-测试丰网速运6',
          'fw002-丰网测试002',
          'FW3423423-丰网长宁测试',
        ],
        ht: [
          '011500-和林格尔县',
          '052500-深泽',
          '052511-百世深泽',
          '10001-淞沪网点',
          '164200-孙吴县',
          '212200-扬中',
          '239003-滁州城南分部',
          '310030-百世测试对接',
          '315203-江北一部',
          '352000-宁德A站',
          '410093-平江',
          '66666-全是6',
          '96444-迎宾网点',
        ],
        jt: [
          '2898300-海南老城网点',
          '333333-订单',
          '455101-合肥转运中心',
          '96555-信丰网点',
          'jt0002-极兔测试1123',
          'jt001-极兔快递01路',
        ],
        sto: [
          '0001111-测试11111',
          '226400-测试网点编号',
          '311113-浙江测试36',
          '400000-90',
          '415700-西藏拉萨公司',
          '616750-四川喜德公司',
          '900000-666',
          '900005-测试',
          '900006-总部测试1',
          '900009-测试',
        ],
        tt: [
          '12121-12112121',
          '1213131-假天',
          '234567-天天测试',
          '96333-和元组网点',
          'KT31117000-深泽分公司',
          'KT57811000-天天',
        ],
        yd: [
          '052500-测试深泽',
          '10001-淞沪网点',
          '12343543-韵达2',
          '210369-江苏南京滨江新城公司',
          '323700-丽水韵达网点',
          '3243434-韵达',
          '352100-福建宁德公司',
          '414004-湖南平江县公司',
          '520220-赣州网点',
          '528401-广东中山公司南区分部',
          '581795-河北主城区公司深泽县服务部',
          '615602-四川主城区公司喜德县服务部',
          '96123-廖溪网点',
        ],
        yt: [
          '043205-吉林省吉林市永吉县口前乡分部',
          '100001-淞沪网点',
          '311009-河北省石家庄市深泽县',
          '3454355345-是鬼斧神工',
          '352111-圆通测试网点',
          '430001-圆通测试！！！！！',
          '432008-吉林省吉林市永吉县',
          '444444-狂鼠中心',
          '66666555-666666555',
          '730008-湖南省岳阳市平江县公司',
          '730824-平江伍市镇',
          '8080-圆通兴国网点',
          '834016-四川省凉山彝族自治州喜德县公司',
          '876013-云南省昆明市关上公司',
          '898037-线路测试网点',
          '898819-海南省澄迈县白莲镇',
          '909090-圆通测试',
          '96111-埠头网点',
          '99922-这里是测试网点',
        ],
        zt: [
          '10001-淞虹网点',
          '12222222-测试申通',
          '432008-吉林省吉林市永吉县',
          '456754-江西网点',
          '51405-昆山花桥',
          '550002-假花桥',
          '57720-乐清',
          '666666-666666',
          '75770-测试75770',
          '9600-兴国网点',
        ],
        zykd: [
          '100001-淞虹网点',
          '123-众邮下一站',
          '23334-7777',
          '24-2424',
          'JJW000518-地平线网点',
          'JJW011301-秦皇岛海港公司',
        ],
      },
    });
  },
  // 获取总览数据
  'POST /v1/TotalDistribution/MultiBrandQueryInfo/getSumTotal': (req, res) => {
    if (req.body.time === '0') {
      res.send({
        code: 0,
        msg: '请求成功',
        data: {
          tt: {
            delivery: {
              success: `123${req.body.time}`,
              failed: `12${req.body.time}`,
            },
            arrival: {
              success: `153${req.body.time}`,
              failed: `15${req.body.time}`,
            },
            sign: {
              success: `32${req.body.time}`,
              failed: `74${req.body.time}`,
            },
          },
          zt: {
            delivery: {
              success: `123${req.body.time}`,
              failed: `12${req.body.time}`,
            },
            arrival: {
              success: `153${req.body.time}`,
              failed: `15${req.body.time}`,
            },
            sign: {
              success: `32${req.body.time}`,
              failed: `74${req.body.time}`,
            },
          },
          sto: {
            delivery: {
              success: `123${req.body.time}`,
              failed: `12${req.body.time}`,
            },
            arrival: {
              success: `153${req.body.time}`,
              failed: `15${req.body.time}`,
            },
            sign: {
              success: `32${req.body.time}`,
              failed: `74${req.body.time}`,
            },
          },
          yt: {
            delivery: {
              success: `123${req.body.time}`,
              failed: `12${req.body.time}`,
            },
            arrival: {
              success: `153${req.body.time}`,
              failed: `15${req.body.time}`,
            },
            sign: {
              success: `32${req.body.time}`,
              failed: `74${req.body.time}`,
            },
          },
        },
      });
    } else {
      res.send({
        code: 0,
        msg: '请求成功',
        data: {
          // "tt": {
          //   "delivery": "123" + req.body.time,
          //   "arrival": "153" + req.body.time
          // },
          // "zt": {
          //   "delivery": "123" + req.body.time,
          //   "arrival": "176" + req.body.time
          // },
          // "sto": {
          //   "delivery": "134" + req.body.time,
          //   "arrival": "177" + req.body.time
          // },
          // "yt": {
          //   "delivery": "122" + req.body.time,
          //   "arrival": "187" + req.body.time
          // }
        },
      });
    }
  },
  // 获取近30天到件趋势
  'POST /v1/TotalDistribution/MultiBrandQueryInfo/getMultiBrandTrend': (req, res) => {
    res.send({
      code: 0,
      msg: '请求成功',
      data: {
        tt: [
          {
            dt: '2018-12-25',
            nbs: '104',
          },
          {
            dt: '2019-03-08',
            nbs: '1',
          },
          {
            dt: '2019-03-12',
            nbs: '2',
          },
          {
            dt: '2019-02-12',
            nbs: '2',
          },
          {
            dt: '2019-03-13',
            nbs: '2',
          },
          {
            dt: '2019-03-14',
            nbs: '2',
          },
          {
            dt: '2019-03-15',
            nbs: '2',
          },
          {
            dt: '2019-03-16',
            nbs: '2',
          },
        ],
        yd: [
          {
            dt: '2018-12-21',
            nbs: '14',
          },
          {
            dt: '2019-03-28',
            nbs: '5',
          },
          {
            dt: '2019-03-12',
            nbs: '8',
          },
          {
            dt: '2019-03-14',
            nbs: '2',
          },
          {
            dt: '2019-03-15',
            nbs: '2',
          },
          {
            dt: '2019-03-16',
            nbs: '2',
          },
        ],
        sto: [
          {
            dt: '2019-03-11',
            nbs: '64',
          },
          {
            dt: '2019-03-12',
            nbs: '18',
          },
          {
            dt: '2019-03-13',
            nbs: '22',
          },
          {
            dt: '2019-03-14',
            nbs: '22',
          },
          {
            dt: '2018-12-11',
            nbs: '22',
          },
          {
            dt: '2018-12-19',
            nbs: '22',
          },
        ],
      },
    });
    // res.send({
    //   code: 0,
    //   msg: '请求成功',
    //   data: {
    //     all: getNumbers(100, 500),
    //     zhong: getNumbers(100, 222),
    //     tian: getNumbers(100, 222),
    //     bai: getNumbers(100, 222),
    //     yun: getNumbers(100, 222),
    //     yuan: getNumbers(100, 222),
    //     shen: getNumbers(100, 222)
    //   }
    // })
  },
  // 获取验证码
  'POST /v1/TotalDistribution/ExpressCompanyInfo/sendVerifyCode': (req, res) => {
    res.send({
      code: 0,
      msg: '验证码发送成功',
      data: {},
    });
  },
  // 获取下一站设置列表
  'POST /v1/TotalDistribution/ExpressCompanyInfo/nextList': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: [
        {
          id: '6',
          kb_id: '7',
          brand: 'yd',
          branch_code: '052500',
          station_type: '1',
          station_code: '5435345',
          station_name: '发件测试中心',
          create_time: '2020-06-22 15:39:50',
          update_time: '2020-06-22 15:39:50',
        },
        {
          id: '7',
          kb_id: '7',
          brand: 'yd',
          branch_code: '052500',
          station_type: '1',
          station_code: '5435345',
          station_name: '发件测试中心',
          create_time: '2020-06-22 15:39:57',
          update_time: '2020-06-22 15:39:57',
        },
        {
          id: '8',
          kb_id: '7',
          brand: 'yd',
          branch_code: '052500',
          station_type: '1',
          station_code: '54353451',
          station_name: '发件测试中心',
          create_time: '2020-06-22 15:52:11',
          update_time: '2020-06-22 15:52:11',
        },
        {
          id: '9',
          kb_id: '7',
          brand: 'yd',
          branch_code: '052500',
          station_type: '1',
          station_code: '543534512',
          station_name: '发件测试中心',
          create_time: '2020-06-22 16:00:45',
          update_time: '2020-06-22 16:00:45',
        },
        {
          id: '10',
          kb_id: '7',
          brand: 'yd',
          branch_code: '052500',
          station_type: '1',
          station_code: '5435345122',
          station_name: '发件测试中心',
          create_time: '2020-06-22 16:00:56',
          update_time: '2020-06-22 16:00:56',
        },
        {
          id: '11',
          kb_id: '7',
          brand: 'yd',
          branch_code: '052500',
          station_type: '1',
          station_code: '54353451222',
          station_name: '发件测试中心',
          create_time: '2020-06-22 16:02:46',
          update_time: '2020-06-22 16:02:46',
        },
        {
          id: '12',
          kb_id: '7',
          brand: 'yd',
          branch_code: '052500',
          station_type: '1',
          station_code: '54353451222fs舒服舒服',
          station_name: '发件测试中心',
          create_time: '2020-06-22 16:12:53',
          update_time: '2020-06-22 16:12:53',
        },
      ],
    });
  },
  // 下一站设置，删除
  'POST /v1/TotalDistribution/ExpressCompanyInfo/nextDelete': (req, res) => {
    res.send({
      code: 0,
      msg: '删除成功',
      data: {},
    });
  },
  // 下一站设置，添加
  'POST /v1/TotalDistribution/ExpressCompanyInfo/nextConfig': (req, res) => {
    res.send({
      code: 0,
      msg: '添加成功',
      data: {},
    });
  },
  // 业务员看板
  'POST /v1/TotalDistribution/CourierBulletinBoard/getAllBrand': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: [
        {
          brand_name: '申通',
          brand_code: 'sto',
        },
        {
          brand_name: '中通',
          brand_code: 'zt',
        },
      ],
    });
  },
  // 业务员看板，表格数据，快递员看板总览
  'POST /v1/TotalDistribution/CourierBulletinBoard/getBulletinBoard': (req, res) => {
    const result = [];
    Array.from({
      length: 40,
    }).forEach((item, index) => {
      result.push({
        name: 'xqx',
        phone: `1527084058${index}`,
        arrival: {
          success: 100,
          failed: 200,
        },
        delivery: {
          success: 100,
          failed: 200,
        },
        sign: {
          success: 100,
          failed: 200,
        },
        send: {
          success: 100,
          failed: 200,
        },
      });
    });

    res.send({
      code: 0,
      msg: 'success',
      data: {
        list: result,
        ct: 40,
        pageSize: 20,
        page: 1,
      },
    });
  },
  // 业务员看板，业务员搜索接口
  'POST /v1/TotalDistribution/kanban/searchOperator': (req, res) => {
    const result = [];
    Array.from({
      length: 40,
    }).forEach((item, index) => {
      result.push({
        key: `${index}`,
        value: `业务员${index}`,
      });
    });

    res.send({
      code: 0,
      msg: 'success',
      data: {
        list: result,
      },
    });
  },
  // 业务员看板，重传未推送接口
  'POST /v1/TotalDistribution/CourierBulletinBoard/reUploadFail': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  // 巴枪设备编号，获取上一站信息及巴枪设备编号
  'POST /Api/Company/getCompanyInfoByBranchCode': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: [
        {
          id: '137_yd',
          kb_id: '37',
          previous_station_code: '123456',
          next_station_code: '',
          brand: 'yd',
          branch_code: '2123',
          branch_name: '12321',
          next_station_name: '',
          previous_station_name: '上一站123',
          user_name: '',
          user_pwd: '',
          arrival_gun_code: '',
          delivery_gun_code: '11111111111111111',
          disabled: '0',
          update_time: '2020-07-29 16:29:19',
          create_time: '2020-07-29 15:22:52',
          dev_id: '',
          dev_imei: '',
          status: '0',
        },
      ],
    });
  },
  // 巴枪注册，发起注册
  'POST /Api/Company/postRegister': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  // 巴枪注册，获取巴枪注册列表
  'POST /Api/DeviceNumber/getDeviceNumberList': (req, res) => {
    const result = [];
    Array.from({
      length: 5,
    }).forEach((item, index) => {
      result.push({
        id: `${index}_sto`,
        shop_id: `${index}1`,
        company_info_id: `${index}2`,
        branch_code: '1111',
        branch_name: '网点名称',
        brand: 'zt',
        user_name: '222',
        phone: '13166252987',
        device_number: `1602812396-${index}`,
        status: `${index % 3 === 0 ? (index % 2 === 0 ? 3 : 1) : 2}`,
        create_at: '2020-10-16 09:39:56',
        code: '12121',
        phone_brand: '凯立',
        phone_model: 'K2',
      });
    });
    res.send({
      code: 0,
      msg: 'success',
      data: result,
    });
  },
  // 巴枪设备编号，中通获取巴枪设备列表
  'POST /Api/DeviceNumber/getDeviceNumberListForZt': (req, res) => {
    const result = [];
    Array.from({
      length: 5,
    }).forEach((item, index) => {
      result.push({
        id: `${index}_zt`,
        shop_id: `${index}1`,
        company_info_id: `${index}2`,
        branch_code: '1111',
        branch_name: '网点名称_中通',
        brand: 'zt',
        user_name: '222',
        phone: '13166252987',
        device_number: `1602812396-${index}_中通`,
        status: `${index % 3 === 0 ? (index % 2 === 0 ? 3 : 1) : 2}`,
        create_at: '2020-10-16 09:39:56',
        code: '12121',
        phone_brand: '凯立_中通',
        phone_model: 'K2',
      });
    });
    res.send({
      code: 0,
      msg: 'success',
      data: result,
    });
  },
  // 巴枪注册，删除未通过或通过的审核记录
  'POST /Api/DeviceNumber/deleteDeviceNumber': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  // 巴枪设备编号，中通删除未通过或通过的审核记录
  'POST /Api/DeviceNumber/deleteDeviceNumberForZt': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  // 巴枪注册，发起注册接口
  'POST /Api/DeviceNumber/saveDeviceNumber': (req, res) => {
    res.send({
      code: 0,
      msg:
        '设备注册已提交，请到“中天系统-产品导航-资产-设备管理-快宝新增数据审核”中进行审核。设备SN码：xxx。审核通过后即可使用。',
      data: {},
    });
  },
  // 巴枪设备编号，添加编号（中通专用）
  'POST /Api/DeviceNumber/saveDeviceNumberForZt': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  // 巴枪设备编号，添加编号（圆通专用）
  'POST /Api/DeviceNumber/saveDeviceNumberForYt': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  // 巴枪注册，获取工号下拉信息
  'POST /Api/Courier/getZtoCourierInfo': (req, res) => {
    const result = [];
    Array.from({
      length: 5,
    }).forEach((item, index) => {
      result.push({
        courier_name: `张三${index}`,
        courier_phone: `***********${index}`,
        gun_account: `57720.456${index}`,
      });
    });
    res.send({
      code: 0,
      msg: 'success',
      data: result,
    });
  },
  // 申通获取巴枪型号
  'POST /Api/PdaDevice/getPdaDeviceList': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: [
        {
          name: '凯立',
          type: 'K2',
        },
      ],
    });
  },
  // 巴枪扫描，获取扣费信息
  'POST /v1/TotalDistribution/ExpressCompanyInfo/getUserInfo': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {
        is_gp: '1',
        is_free: '2',
        money: '0.11',
        deduction_type: '2',
      },
    });
  },
  // 巴枪扫描，编辑扣费类型
  'POST /v1/TotalDistribution/ExpressCompanyInfo/editDeductionType': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  // 巴枪配置，检查申通巴枪工号是否存在
  'POST /v1/TotalDistribution/GunInformation/checkStoCmInfo': (req, res) => {
    const account = req.body.gun_account;
    let code;
    let data = '';
    let msg = '';
    switch (account) {
      case '1111':
        code = 1012; // 工号不存在
        msg =
          '网点XXX下维护的工号xxx在梧桐系统内不存在或者找不到工号对应的手机号，无法验证。请在梧桐系统-快递业务-网点信息维护-用户资料内维护';
        break;
      case '2222':
        code = 0; // 工号校验通过
        break;
      default:
        code = 1013; // 手机号不一致
        data = '***********';
        break;
    }
    res.send({
      code,
      msg,
      data,
    });
  },
  // 巴枪配置，添加申通工号验证，发送短信
  'POST /v1/TotalDistribution/ExpressCompanyInfo/sendVerifyCodeForGunSet': (req, res) => {
    res.send({
      code: 0,
      msg: '发送成功',
      data: {},
    });
  },
  // 巴枪配置，添加申通工号验证，验证手机
  'POST /v1/TotalDistribution/ExpressCompanyInfo/stoVerifyForGunSet': (req, res) => {
    const code = req.body.verify_code === '1' ? 0 : 1;
    res.send({
      code,
      msg: '验证成功',
      data: {},
    });
  },
  // 共配权限管理，获取列表
  'POST /Api/ChinaPost/ZyGpJur/getListBySearch': (req, res) => {
    const result = [];
    Array.from({
      length: 40,
    }).forEach((item, index) => {
      result.push({
        id: `${index}`,
        province: `省${index}`,
        city: `市${index}`,
        county: `县${index}`,
        is_gp: index / 2 === 0 ? 0 : 1,
      });
    });
    res.send({
      code: 0,
      msg: 'success',
      data: {
        list: result,
        page: 1,
        count: 40,
        pageSize: 20,
      },
    });
  },
  // 共配权限，修改权限
  'POST /Api/ChinaPost/ZyGpJur/modifyGp': (req, res) => {
    res.send({
      code: 0,
      msg: '修改成功',
      data: {},
    });
  },
  // 共配权限，获取不同品牌的共配权限
  'POST /v1/TotalDistribution/ExpressCompanyInfo/getScanPower': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: [
        {
          type: 1,
          content: '收件',
        },
        {
          type: 2,
          content: '派件',
        },
        {
          type: 3,
          content: '签收',
        },
        {
          type: 4,
          content: '问题件',
        },
        {
          type: 5,
          content: '到件',
        },
        {
          type: 6,
          content: '发件',
        },
        {
          type: 0,
          content: '自定义扫描',
        },
      ],
    });
  },
  // 自定义扫描，获取扫描纪录列表
  'POST /v1/TotalDistribution/CustomScan/getScanRecord': (req, res) => {
    const result = [];
    Array.from({
      length: 40,
    }).forEach((item, index) => {
      result.push({
        id: `${index}`,
        cm_id: '428220',
        brand: 'sto',
        cm_phone: '***********',
        cm_name: '张三',
        cm_code: '9000060006',
        branch_no: '900006',
        shop_name: '总部测试',
        waybill_no: '588000000554',
        waybill_type: '到达乡镇',
        operator_code: '9000060006',
        scan_option: '签收人',
        operator_name: '快宝193',
        fail_count: '0',
        fail_desc: '',
        send_status: '1',
        site_code: '',
        site_name: '',
        sign_type: '',
        create_time: '2021-01-19 11:20:15',
        scan_time: '2021-01-18 00:00:00',
        update_time: '2021-01-19 11:45:24',
      });
    });
    res.send({
      code: 0,
      msg: 'success',
      data: {
        page: 1,
        list: result,
        total: 40,
      },
    });
  },
  // 自定义扫描，获取扫描类型列表
  'POST /v1/TotalDistribution/CustomScan/getScanTypeList': (req, res) => {
    const result = [];
    Array.from({
      length: 40,
    }).forEach((item, index) => {
      result.push({
        id: index,
        shop_id: `${index}_shop_id`,
        scan_type: `${index}发往乡镇`,
        scan_option: `${index}大山镇`,
        is_del: index,
        create_time: '2021-01-18 14:35:53',
      });
    });
    res.send({
      code: 0,
      msg: 'success',
      data: result,
    });
  },
  // 自定义扫描，添加扫描类型
  'POST /v1/TotalDistribution/CustomScan/newBuildScanType': (req, res) => {
    res.send({
      code: 0,
      msg: '添加成功！',
      data: {},
    });
  },
  // 自定义扫描，删除扫描类型
  'POST /v1/TotalDistribution/CustomScan/editScanType': (req, res) => {
    res.send({
      code: 0,
      msg: '删除成功！',
      data: {},
    });
  },
  // 自定义扫描，扫描类型下拉接口
  'POST /v1/TotalDistribution/CustomScan/getScanOptionList': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: ['站点', '业务员', '签收人', '公交车辆'],
    });
  },
  // 自定义扫描，扫描纪录，扫描类型下拉
  'POST /v1/TotalDistribution/CustomScan/getScanType': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: [
        {
          scan_type: '到达乡镇',
        },
        {
          scan_type: '发往乡镇',
        },
        {
          scan_type: '客户领件',
        },
      ],
    });
  },
  // 共配，获取物流详情
  'POST /v1/TotalDistribution/CustomScan/getExpressInfo': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: [
        {
          time: '2020-05-24 18:50:57',
          context: '【南台】（18242226199、13154252506） 的 廉兴业（13154252506） 已揽收',
          status: 'collected',
        },
        {
          time: '2020-05-24 20:53:10',
          context: '快件离开 【南台】 已发往 【盘锦中转】',
          status: 'sending',
          shop_info: {
            shop_name: '南台',
            shop_code: '240044',
            shop_phone: '18242226199、13154252506',
          },
        },
        {
          time: '2020-05-24 22:40:37',
          context: '快件已经到达 【盘锦中转】',
          status: 'sending',
          shop_info: {
            shop_name: '盘锦中转',
            shop_code: '42700',
            shop_phone: '0427-3338915',
          },
        },
        {
          time: '2020-05-24 22:40:39',
          context: '快件离开 【盘锦中转】 已发往 【北京】',
          status: 'sending',
          shop_info: {
            shop_name: '盘锦中转',
            shop_code: '42700',
            shop_phone: '0427-3338915',
          },
        },
        {
          time: '2020-05-25 08:53:09',
          context: '快件已经到达 【北京】',
          status: 'sending',
          shop_info: {
            shop_name: '北京',
            shop_code: '01001',
            shop_phone: '010-86483232',
          },
        },
        {
          time: '2020-05-25 09:37:10',
          context: '快件离开 【北京】 已发往 【北京朝阳亮马桥】',
          status: 'sending',
          shop_info: {
            shop_name: '北京',
            shop_code: '01001',
            shop_phone: '010-86483232',
          },
        },
        {
          time: '2020-05-25 10:15:39',
          context: '快件已经到达 【北京朝阳亮马桥】',
          status: 'sending',
          shop_info: {
            shop_name: '北京朝阳亮马桥',
            shop_code: '16370',
            shop_phone: '010-67448763、15321118629',
          },
        },
        {
          time: '2020-05-25 14:09:45',
          context:
            '【北京朝阳亮马桥】 的王涛（13521263969） 正在第1次派件, 请保持电话畅通,并耐心等待（95720为中通快递员外呼专属号码，请放心接听）',
          status: 'delivering',
          shop_info: {
            shop_name: '北京朝阳亮马桥',
            shop_code: '16370',
            shop_phone: '010-67448763、15321118629',
          },
        },
        {
          time: '2020-05-25 14:31:55',
          context:
            '快件已送达【菜鸟的枣营南里20号楼桥旁北侧菜鸟智能柜【自提柜】】, 如有问题请电联:（13521263969）, 投诉电话:（057126883287）, 感谢您使用中通快递, 期待再次为您服务!',
          status: 'allograph',
          shop_info: {
            shop_name: '菜鸟的枣营南里20号楼桥旁北侧菜鸟智能柜【自提柜】',
            shop_code: '1033383_2',
            shop_phone: '057126883287',
          },
        },
        {
          time: '2020-05-26 05:46:03',
          context:
            '已签收, 签收人凭取货码签收, 如有疑问请电联:（13521263969）, 投诉电话:（057126883287）, 您的快递已经妥投。风里来雨里去, 只为客官您满意。上有老下有小, 赏个好评好不好？【请在评价快递员处帮忙点亮五颗星星哦~】',
          status: 'signed',
          shop_info: {
            shop_name: '菜鸟的枣营南里20号楼桥旁北侧菜鸟智能柜【自提柜】',
            shop_code: '1033383_2',
            shop_phone: '057126883287',
          },
        },
      ],
    });
  },
  // 共配，密钥配置
  'POST /Api/Agent/getAgentList': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: [
        {
          id: 1,
          shop_id: 1,
          key: 'caklsd123',
          name: '快宝云打印1',
          create_time: '2021-5-27 15:34:34',
          status: '1',
          gu_id: '1123123123',
        },
        {
          id: 2,
          shop_id: 2,
          key: 'caklsd1232222',
          name: '快宝云打印2',
          create_time: '2021-5-27 15:34:34',
          status: '2',
          gu_id: '1123123123',
        },
        {
          id: 3,
          shop_id: 3,
          key: 'caklsd123333333',
          name: '快宝云打印3',
          create_time: '2021-5-27 15:34:34',
          status: '3',
          gu_id: '1123123123',
        },
      ],
    });
  },
  // 共配，密钥配置，添加密钥
  'POST /Api/Agent/addAgent': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  // 共配，密钥配置，刷新状态
  'POST /Api/Agent/editAgentStatus': (req, res) => {
    res.send({
      code: 0,
      msg: '失败',
      data: {},
    });
  },
  // 共配，密钥配置，删除密钥
  'POST /Api/Agent/delAgent': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  // 共配，数据统计，扫描统计列表
  'POST /v1/TotalDistribution/MultiBrandQueryInfo/getSumTotalByCourier': (req, res) => {
    const list = [];
    Array.from({ length: 40 }).forEach((_, index) => {
      list.push({
        courier_name: `扫描员${index}`,
        courier_phone: `1337777888${index}`,
        sort: index,
        sto: {
          delivery: 10,
          arrival: 11,
          send: 12,
          sign: 13,
        },
        sum: {
          delivery: 9,
          arrival: 8,
          send: 7,
        },
      });
    });
    res.send({
      code: 0,
      msg: 'success',
      data: list,
    });
  },
  // 共配，数据统计，派件统计列表
  'POST /v1/TotalDistribution/MultiBrandQueryInfo/getSumTotalByOperator': (req, res) => {
    const list = [];
    Array.from({ length: 40 }).forEach((_, index) => {
      list.push({
        courier_name: `派件员${index}`,
        courier_phone: `1337777888${index}`,
        sort: index,
        sto: {
          delivery: 10,
        },
        sum: {
          delivery: 9,
          arrival: 8,
          send: 7,
        },
      });
    });
    res.send({
      code: 0,
      msg: 'success',
      data: list,
    });
  },
  // 共配，数据监控，扫描识别记录列表
  'POST /v1/TotalDistribution/OperationRecord/getOperationRecord': (req, res) => {
    const { page } = req.body;
    const list = [];
    Array.from({ length: 40 }).forEach((_, index) => {
      list.push({
        id: index,
        waybill_no: `462263902271431${index}`,
        brand: '韵达快递',
        brand_code: 'yd',
        phone: '15201946772',
        scan_type: '到派件',
        scan_code: '3',
        scan: 'send',
        noticeType: '特殊件类型',
        device: '设备型号',
        create_time: '2022-03-03 17:11:48',
        auto_switch: '开启',
        shop_id: '7',
        cm_name: '潘用伟772',
        version_code: '1.000',
      });
    });
    res.send({
      code: 0,
      msg: 'success',
      data: {
        list,
        page,
        pageSize: 20,
        total: 40,
      },
    });
  },
  // 共配，数据监控，获取所有类型
  'POST /v1/TotalDistribution/OperationRecord/getAllType': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: [
        {
          type: '全部',
        },
        {
          type: '收件',
        },
        {
          type: '发件',
        },
        {
          type: '到件',
        },
        {
          type: '派件',
        },
        {
          type: '签收',
        },
        {
          type: '问题件',
        },
        {
          type: '到发件',
        },
        {
          type: '到派件',
        },
        {
          type: '自定义扫描',
        },
        {
          type: '三合一扫描',
        },
        {
          type: '二合一扫描',
        },
        {
          type: '拍照签收',
        },
      ],
    });
  },
  // 共配，数据监控，获取所有支持的品牌
  'POST /v1/TotalDistribution/OperationRecord/getAllBrand': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: [
        {
          brand: 'all',
          name: '全部品牌',
        },
        {
          brand: 'sto',
          name: '申通快递',
        },
        {
          brand: 'zt',
          name: '中通快递',
        },
        {
          brand: 'yt',
          name: '圆通速递',
        },
        {
          brand: 'ht',
          name: '百世快递',
        },
        {
          brand: 'yd',
          name: '韵达快递',
        },
        {
          brand: 'jt',
          name: '极兔快递',
        },
        {
          brand: 'zykd',
          name: '众邮快递',
        },
        {
          brand: 'ems',
          name: '邮政快递',
        },
        {
          brand: 'fw',
          name: '丰网速运',
        },
      ],
    });
  },
  'POST /Api/Company/getJtNo': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: ['JT_REN_WU_HAO'],
    });
  },
  'POST /Api/Company/setJtNo': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  'POST /v1/TotalDistribution/ExpressCompanyInfo/getSenderByKbId': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {
        id: 'id',
        kb_id: 'kb_id',
        branch_code: '网点编号',
        branch_name: '网点名称',
        cm_phone: '业务员手机',
        cm_name: '业务员名称',
        brand: '品牌',
        gun_account: '巴枪账号',
        update_time: 'update_time',
        create_time: 'create_time',
      },
    });
  },
  // 法派设置，获取工号下拉信息
  'POST /v1/TotalDistribution/ExpressCompanyInfo/getCourierListByShopId': (req, res) => {
    const result = [];
    Array.from({
      length: 5,
    }).forEach((item, index) => {
      result.push({
        courier_name: `张三${index}`,
        courier_phone: `***********${index}`,
        courier_id: index,
        courier_no: `57720.456${index}`,
        branch_code: '工号',
        gun_account: `57720.456${index}`,
        brand: '申通',
        gp_user_config_id: `gp_user_config_id_${index}`,
      });
    });
    res.send({
      code: 0,
      msg: 'success',
      data: result,
    });
  },
  // 法派设置，添加业务员
  'POST /v1/TotalDistribution/ExpressCompanyInfo/addSender': (req, res) => {
    setTimeout(() => {
      res.send({
        code: 0,
        msg: 'success',
        data: {},
      });
    }, 1500);
  },
  // 法派设置，删除业务员
  'POST /v1/TotalDistribution/ExpressCompanyInfo/delSender': (req, res) => {
    setTimeout(() => {
      res.send({
        code: 0,
        msg: 'success',
        data: {},
      });
    }, 1500);
  },
  // 共配授权
  'POST /Api/Company/editDisclaimerStatus': (req, res) => {
    setTimeout(() => {
      res.send({
        code: 0,
        msg: 'success',
        data: {},
      });
    }, 1500);
  },
  'POST /Api/Company/setCourierAutoUpload': (req, res) => {
    res.send({ code: 0, msg: 'success' });
  },
  'POST /v1/TotalDistribution/ExpressCompanyInfo/sendSmsCode': (req, res) => {
    res.send({ code: 0, msg: 'success' });
  },
  'POST /v1/TotalDistribution/ExpressCompanyInfo/checkBranch': (req, res) => {
    res.send({ code: 0, msg: 'success', data: { phone: '13333333333' } });
  },
};
