/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

const randomValue = list => list[parseInt(Math.random() * list.length)];
const randomOrderDetail = () => {
  const state = ['已出库', '待出库'];
  const brands = [
    { brand: 'sto', short_name: '申通' },
    { brand: 'zt', short_name: '中通' },
    { brand: 'yt', short_name: '圆通' },
    { brand: 'yd', short_name: '韵达' },
  ];
  const status = randomValue(state);
  const { brand, short_name } = randomValue(brands);
  return {
    status_cn: status,
    brand,
    brand_cn: short_name,
  };
};

export default {
  'POST /Api/YZ/CourierStation/superSearch': (req, res) => {
    const list = [];
    Array.from({
      length: 10,
    }).forEach((item, index) => {
      list.push({
        id: `34488785${index}`,
        waybill_no: `464634553454${index}`,
        phone: '18311111111',
        pickup_code: 'A01',
        cm_id: `cm_id_${index}`,
        ...randomOrderDetail(),
      });
    });
    res.send({
      msg: '暂无结果',
      code: req.body.keyword === '11111111' ? 1 : 0,
      data: list,
    });
  },

  'POST /Api/YZ/CourierStation/searchDetail': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {
        data: [
          {
            create_time: '2025-04-09 17:48:19',
            desc: '入库上传',
            inn_name: '淞虹测试驿站',
            operator: '',
            picture: '',
            picture_invalid: 0,
          },
          {
            create_time: '2025-04-09 17:46:37',
            desc: '暂不通知',
            inn_name: '淞虹测试驿站',
            operator: '',
            picture: '',
            picture_invalid: 0,
          },
          {
            create_time: '2025-04-09 17:46:37',
            desc: '17:44:48 扫描入库',
            inn_name: '淞虹测试驿站',
            operator: '15201946772',
            picture: '',
            picture_invalid: 0,
          },
        ],
        gp_record_list: [
          {
            waybill_no: '73172026156145',
            create_time: '2025-04-09 10:14:31',
            waybill_type: '2',
            waybill_type_text: '派件扫描',
            next_station: '',
            next_station_name: '',
            operator_phone: '15201946772',
            sign_man: '',
            bad_waybill_type: '',
            bad_waybill_desc: '',
            bad_waybill_describe: null,
            delivery_phone: '15201946772',
            delivery_name: '潘用伟',
          },
          {
            waybill_no: '73172026156145',
            create_time: '2025-04-09 10:04:31',
            waybill_type: '5',
            waybill_type_text: '到件扫描',
            next_station: '',
            next_station_name: '',
            operator_phone: '15201946772',
            sign_man: '',
            bad_waybill_type: '',
            bad_waybill_desc: '',
            bad_waybill_describe: null,
            delivery_phone: '',
            delivery_name: '',
          },
        ],
        sorting_record_list: [
          {
            id: '122141',
            sorting_line_arrival_id: '152702',
            shop_id: '7',
            sorting_line_id: '13',
            brand: 'zt',
            waybill: '73172026156145',
            is_match: '1',
            sort_type: '1',
            match_form: '3code',
            address_form: '',
            three_code_form: 'third',
            grid_abnormal_type: '-1',
            remind_type: '',
            remind_type_query_error: '',
            api_3code: '300- G16 124',
            api_3code_phone: '',
            api_3code_channel: '',
            api_3code_query_error: '',
            api_3code_match_error: '',
            api_address: '',
            api_address_phone: '',
            api_address_channel: '',
            api_address_query_error: '',
            api_address_match_error: '',
            ocr_3code: '',
            ocr_3code_query_error: '',
            ocr_3code_match_error: '',
            ocr_address: '',
            ocr_address_query_error: '',
            ocr_address_match_error: '',
            file_path:
              'http://kbtest.oss-cn-hangzhou.aliyuncs.com/sorting/7/2025/04/08/zt/73172026156145_12a0dc4b-e072-46c9-96f9-f35f6c1ea877.jpg',
            hostname: 'test',
            create_at: '2025-04-08 20:46:05',
          },
        ],
      },
    });
    // res.send({
    //   code: 0,
    //   msg: '成功',
    //   data: {
    //     data: [
    //       {
    //         create_time: '2025-04-09 17:48:19',
    //         desc: '入库上传',
    //         inn_name: '淞虹测试驿站',
    //         operator: '',
    //         picture: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/icon_coupon.png',
    //         picture_invalid: 2,
    //       },
    //       {
    //         create_time: '2025-04-09 17:46:37',
    //         desc: '暂不通知',
    //         inn_name: '淞虹测试驿站',
    //         operator: '',
    //         picture: '',
    //         picture_invalid: 0,
    //       },
    //       {
    //         create_time: '2025-04-09 17:46:37',
    //         desc: '17:44:48 扫描入库',
    //         inn_name: '淞虹测试驿站',
    //         operator: '15201946772',
    //         picture: '',
    //         picture_invalid: 0,
    //       },
    //     ],
    //     gp_record_list: [
    //       {
    //         waybill_no: '73172026156145',
    //         create_time: '2025-04-09 10:14:31',
    //         waybill_type: '2',
    //         waybill_type_text: '派件扫描',
    //         next_station: '',
    //         next_station_name: '',
    //         operator_phone: '15201946772',
    //         sign_man: '',
    //         bad_waybill_type: '',
    //         bad_waybill_desc: '',
    //         bad_waybill_describe: null,
    //         delivery_phone: '15201946772',
    //         delivery_name: '潘用伟',
    //         picture: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/icon_coupon.png',
    //         picture_invalid: 2,
    //       },
    //       {
    //         waybill_no: '73172026156145',
    //         create_time: '2025-04-09 10:04:31',
    //         waybill_type: '5',
    //         waybill_type_text: '到件扫描',
    //         next_station: '',
    //         next_station_name: '',
    //         operator_phone: '15201946772',
    //         sign_man: '',
    //         bad_waybill_type: '',
    //         bad_waybill_desc: '',
    //         bad_waybill_describe: null,
    //         delivery_phone: '',
    //         delivery_name: '',
    //       },
    //     ],
    //   },
    // });
  },
  'POST /Api/SuperSearch/detail': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {
        gp_data_list: [
          {
            steid: '164676',
            inn_id: '0',
            kb_code: '000202017',
            cm_id: '2775368',
            cm_phone: '15023657418',
            cm_name: '哦豁',
            cm_code: '02853415',
            branch_no: '31002112',
            shop_name: '彭埠营业部',
            dev_id: '',
            dev_imei: '||3752fda19dcdf495f7dd56508f65f757',
            waybill_no: '1231584929543',
            waybill_type: '2',
            forward_station: '',
            forward_station_name: '',
            next_station: '',
            next_station_name: '',
            create_time: '2025-05-08 15:00:01',
            operator_kb_code: '000202017',
            operator_name: '哦豁',
            operator_code: '02853415',
            bad_waybill_code: null,
            bad_waybill_type: null,
            scan_time: '2025-05-08 14:57:50',
            latitude: '0000.000000',
            longitude: '0000.000000',
            update_time: '2025-05-08 17:28:04',
            fail_count: '0',
            fail_desc: '该邮件是标记件',
            send_status: '1',
            source: '1',
            '3rd_code': '',
            sign_type: null,
            sign_ext: '',
            sign_pic: '',
            group_name: '',
            shop_id: '7',
            sign_ext2: '',
            post: '0',
            brand: 'ems',
            courier_phone: '15023657418',
            sign_pic_multi: [],
            scan_type: '上传',
            auto_upload: 0,
            operation_platform: '狂扫',
            waybill_type_name: '派件',
            notice_type_name: '拦截件',
          },
          {
            steid: '164675',
            inn_id: '0',
            kb_code: '000202017',
            cm_id: '2775368',
            cm_phone: '15023657418',
            cm_name: '哦豁',
            cm_code: '02853415',
            branch_no: '31002112',
            shop_name: '彭埠营业部',
            dev_id: '',
            dev_imei: '||5fb3dc992b06caeac145fe052842174b',
            waybill_no: '1231584929543',
            waybill_type: '5',
            forward_station: '23232',
            forward_station_name: '邮政上一站',
            next_station: '',
            next_station_name: '',
            create_time: '2025-05-08 14:57:53',
            operator_kb_code: null,
            operator_name: '哦豁',
            operator_code: '',
            bad_waybill_code: null,
            bad_waybill_type: null,
            scan_time: '2025-05-08 14:47:50',
            latitude: '0000.000000',
            longitude: '0000.000000',
            update_time: '2025-05-08 17:28:05',
            fail_count: '0',
            fail_desc: '该邮件是标记件',
            send_status: '1',
            source: '1',
            '3rd_code': '',
            sign_type: null,
            sign_ext: '',
            sign_pic: '',
            group_name: '',
            shop_id: '7',
            sign_ext2: '',
            post: '0',
            brand: 'ems',
            courier_phone: '15023657418',
            sign_pic_multi: [],
            scan_type: '上传',
            auto_upload: 0,
            operation_platform: '狂扫',
            waybill_type_name: '到件',
            notice_type_name: '拦截件',
          },
          {
            steid: '164260',
            inn_id: '0',
            kb_code: '000215000',
            cm_id: '2775241',
            cm_phone: '15201946772',
            cm_name: '潘用伟',
            cm_code: '3100211201688',
            branch_no: '31002112',
            shop_name: '彭埠营业部',
            dev_id: 'K74649983476191',
            dev_imei: '6b927430-5566-31cb-a4d2-b6cd8de4aa76||fb3031108a1cc16df0f360123b3e627f',
            waybill_no: '1231584929543',
            waybill_type: '3',
            forward_station: '',
            forward_station_name: '',
            next_station: '',
            next_station_name: '',
            create_time: '2025-05-06 11:48:00',
            operator_kb_code: null,
            operator_name: '潘用伟',
            operator_code: '',
            bad_waybill_code: null,
            bad_waybill_type: null,
            scan_time: '2025-05-06 11:47:52',
            latitude: '0000.000000',
            longitude: '0000.000000',
            update_time: '2025-05-06 11:48:46',
            fail_count: '0',
            fail_desc:
              '未获取到邮政验证信息，请至APP>共配>巴枪工号管理>邮政品牌内配置设备编码等信息验证',
            send_status: '1',
            source: '0',
            '3rd_code': '',
            sign_type: '物业代收',
            sign_ext: '',
            sign_pic: '',
            group_name: '',
            shop_id: '7',
            sign_ext2: '',
            post: '0',
            brand: 'ems',
            courier_phone: '15201946772',
            sign_pic_multi: [],
            scan_type: '签收',
            auto_upload: 0,
            operation_platform: '快递员APP',
            waybill_type_name: '签收',
            notice_type_name: '拦截件',
          },
          {
            steid: '164259',
            inn_id: '0',
            kb_code: '000215000',
            cm_id: '2775241',
            cm_phone: '15201946772',
            cm_name: '潘用伟',
            cm_code: '3100211201688',
            branch_no: '31002112',
            shop_name: '彭埠营业部',
            dev_id: 'K74649983476191',
            dev_imei: '6b927430-5566-31cb-a4d2-b6cd8de4aa76||d95891ba2635331af984ac394e83d991',
            waybill_no: '1231584929543',
            waybill_type: '2',
            forward_station: '',
            forward_station_name: '',
            next_station: '',
            next_station_name: '',
            create_time: '2025-05-06 11:47:23',
            operator_kb_code: '000215000',
            operator_name: '潘用伟',
            operator_code: '3100211201688',
            bad_waybill_code: null,
            bad_waybill_type: null,
            scan_time: '2025-05-06 11:47:18',
            latitude: '0000.000000',
            longitude: '0000.000000',
            update_time: '2025-05-06 11:48:46',
            fail_count: '0',
            fail_desc:
              '未获取到邮政验证信息，请至APP>共配>巴枪工号管理>邮政品牌内配置设备编码等信息验证',
            send_status: '1',
            source: '0',
            '3rd_code': 'YZ123',
            sign_type: null,
            sign_ext: '',
            sign_pic: '',
            group_name: '',
            shop_id: '7',
            sign_ext2: '',
            post: '0',
            brand: 'ems',
            courier_phone: '15201946772',
            sign_pic_multi: [],
            scan_type: '派件',
            auto_upload: 0,
            operation_platform: '快递员APP',
            waybill_type_name: '派件',
            notice_type_name: '拦截件',
          },
          {
            steid: '164258',
            inn_id: '0',
            kb_code: '000215000',
            cm_id: '2775241',
            cm_phone: '15201946772',
            cm_name: '潘用伟',
            cm_code: '3100211201688',
            branch_no: '31002112',
            shop_name: '彭埠营业部',
            dev_id: 'K74649983476191',
            dev_imei: '6b927430-5566-31cb-a4d2-b6cd8de4aa76||b9307db74783d51a7ea6951a2956be5c',
            waybill_no: '1231584929543',
            waybill_type: '5',
            forward_station: '343',
            forward_station_name: '邮政上一站',
            next_station: '',
            next_station_name: '',
            create_time: '2025-05-06 11:46:05',
            operator_kb_code: null,
            operator_name: '潘用伟',
            operator_code: '3100211201688',
            bad_waybill_code: null,
            bad_waybill_type: null,
            scan_time: '2025-05-06 11:45:56',
            latitude: '0000.000000',
            longitude: '0000.000000',
            update_time: '2025-05-06 11:48:46',
            fail_count: '0',
            fail_desc:
              '未获取到邮政验证信息，请至APP>共配>巴枪工号管理>邮政品牌内配置设备编码等信息验证',
            send_status: '1',
            source: '0',
            '3rd_code': '',
            sign_type: null,
            sign_ext: '',
            sign_pic: '',
            group_name: '',
            shop_id: '7',
            sign_ext2: '',
            post: '0',
            brand: 'ems',
            courier_phone: '15201946772',
            sign_pic_multi: [],
            scan_type: '到件',
            auto_upload: 0,
            operation_platform: '快递员APP',
            waybill_type_name: '到件',
            notice_type_name: '拦截件',
          },
          {
            steid: '163116',
            inn_id: '0',
            kb_code: '000215000',
            cm_id: '2775241',
            cm_phone: '15201946772',
            cm_name: '潘用伟',
            cm_code: '3100211201688',
            branch_no: '31002112',
            shop_name: '彭埠营业部',
            dev_id: '',
            dev_imei: '||11ddaa6b769b0f8f72af0e2dafb0829e',
            waybill_no: '1231584929543',
            waybill_type: '2',
            forward_station: '',
            forward_station_name: '',
            next_station: '',
            next_station_name: '',
            create_time: '2025-04-11 15:54:04',
            operator_kb_code: '000215000',
            operator_name: '潘用伟',
            operator_code: '3100211201688',
            bad_waybill_code: null,
            bad_waybill_type: null,
            scan_time: '2025-04-11 15:51:13',
            latitude: '0000.000000',
            longitude: '0000.000000',
            update_time: '2025-04-11 16:03:38',
            fail_count: '0',
            fail_desc:
              '未获取到邮政验证信息，请至APP>共配>巴枪工号管理>邮政品牌内配置设备编码等信息验证',
            send_status: '1',
            source: '9',
            '3rd_code': 'YZ123',
            sign_type: null,
            sign_ext: '',
            sign_pic: '',
            group_name: '',
            shop_id: '7',
            sign_ext2: '',
            post: '0',
            brand: 'ems',
            courier_phone: '15201946772',
            sign_pic_multi: [],
            scan_type: '',
            auto_upload: 0,
            operation_platform: '新零售补件',
            waybill_type_name: '派件',
            notice_type_name: '拦截件',
          },
          {
            steid: '162089',
            inn_id: '0',
            kb_code: '000215000',
            cm_id: '2775241',
            cm_phone: '15201946772',
            cm_name: '潘用伟',
            cm_code: '3100211201688',
            branch_no: '31002112',
            shop_name: '彭埠营业部',
            dev_id: '',
            dev_imei: '||11ddaa6b769b0f8f72af0e2dafb0829e',
            waybill_no: '1231584929543',
            waybill_type: '5',
            forward_station: '343',
            forward_station_name: '邮政上一站',
            next_station: '',
            next_station_name: '',
            create_time: '2025-04-11 15:51:22',
            operator_kb_code: '000215000',
            operator_name: '潘用伟',
            operator_code: '3100211201688',
            bad_waybill_code: null,
            bad_waybill_type: null,
            scan_time: '2025-04-11 15:41:13',
            latitude: '0000.000000',
            longitude: '0000.000000',
            update_time: '2025-04-11 16:03:38',
            fail_count: '0',
            fail_desc:
              '未获取到邮政验证信息，请至APP>共配>巴枪工号管理>邮政品牌内配置设备编码等信息验证',
            send_status: '1',
            source: '9',
            '3rd_code': '',
            sign_type: null,
            sign_ext: '',
            sign_pic: '',
            group_name: '',
            shop_id: '7',
            sign_ext2: '',
            post: '0',
            brand: 'ems',
            courier_phone: '15201946772',
            sign_pic_multi: [],
            scan_type: '',
            auto_upload: 0,
            operation_platform: '新零售补件',
            waybill_type_name: '到件',
            notice_type_name: '拦截件',
          },
        ],
        sorting_data_list: [
          {
            id: '152764',
            shop_id: '7',
            sorting_line_id: '13',
            sorting_line_code: '18721008363_08',
            waybill: '1231584929543',
            brand: 'ems',
            seq: '47601980-8c12-4fe1-bdb4-a69225c21f87',
            one_code: '',
            two_code: '',
            three_code: '',
            weight: '0',
            grid_code: 'YZ100',
            grid_type: '2',
            actual_grid_type: '0',
            actual_grid_code: '',
            is_accuracy: '0',
            gbt_no: '1110008',
            gbt_scan_time: '2025-05-08 15:43:30',
            dj_time: '2025-05-08 15:43:30',
            dj_status: '0',
            send_time: null,
            send_status: '0',
            pj_time: null,
            pj_status: '0',
            next_dj_time: null,
            next_dj_status: '0',
            next_pj_time: null,
            next_pj_status: '0',
            luoge_time: null,
            queue_data: null,
            addr_keys: '',
            gbt_status: '单号是拦截件',
            match_data: {
              id: '122239',
              sorting_line_arrival_id: '152764',
              shop_id: '7',
              sorting_line_id: '13',
              brand: 'ems',
              waybill: '1231584929543',
              is_match: '0',
              sort_type: '2',
              match_form: '',
              address_form: '',
              three_code_form: '',
              grid_abnormal_type: '6',
              remind_type: '拦截件',
              remind_type_query_error: '',
              api_3code: '',
              api_3code_phone: '',
              api_3code_channel: '',
              api_3code_query_error: '',
              api_3code_match_error: '',
              api_address: '',
              api_address_phone: '',
              api_address_channel: '',
              api_address_query_error: '地址为空，未获得',
              api_address_match_error: '',
              ocr_3code: '',
              ocr_3code_query_error: '',
              ocr_3code_match_error: '',
              ocr_address: '',
              ocr_address_query_error: '网络超时',
              ocr_address_match_error: '',
              file_path:
                'http://kbtest.oss-cn-hangzhou.aliyuncs.com/sorting/7/2025/05/08/ems/1231584929543_47601980-8c12-4fe1-bdb4-a69225c21f87.jpg',
              hostname: 'test',
              other_data: '',
              create_at: '2025-05-08 15:43:30',
              file_path_online: 0,
            },
          },
          {
            id: '152763',
            shop_id: '7',
            sorting_line_id: '13',
            sorting_line_code: '18721008363_08',
            waybill: '1231584929543',
            brand: 'ems',
            seq: 'f3b09ac4-5ed1-455d-80a9-7950dfc4a96a',
            one_code: '',
            two_code: '',
            three_code: '',
            weight: '0',
            grid_code: 'YZ100',
            grid_type: '2',
            actual_grid_type: '0',
            actual_grid_code: '',
            is_accuracy: '0',
            gbt_no: '1110008',
            gbt_scan_time: '2025-05-08 15:38:31',
            dj_time: '2025-05-08 15:38:31',
            dj_status: '0',
            send_time: null,
            send_status: '0',
            pj_time: null,
            pj_status: '0',
            next_dj_time: null,
            next_dj_status: '0',
            next_pj_time: null,
            next_pj_status: '0',
            luoge_time: null,
            queue_data: null,
            addr_keys: '',
            gbt_status: '单号是拦截件1',
            match_data: {
              id: '122237',
              sorting_line_arrival_id: '152763',
              shop_id: '7',
              sorting_line_id: '13',
              brand: 'ems',
              waybill: '1231584929543',
              is_match: '0',
              sort_type: '21',
              match_form: '3code',
              address_form: 'ocr',
              three_code_form: 'third',
              grid_abnormal_type: '6',
              remind_type: '拦截件',
              remind_type_query_error: '',
              api_3code: '123',
              api_3code_phone: '',
              api_3code_channel: '',
              api_3code_query_error: '该品牌不支持接口获取三段码',
              api_3code_match_error: '',
              api_address: '',
              api_address_phone: '',
              api_address_channel: '',
              api_address_query_error: '地址为空，未获得',
              api_address_match_error: '',
              ocr_3code: '123456',
              ocr_3code_query_error: '网络超时',
              ocr_3code_match_error: '',
              ocr_address: '222',
              ocr_address_query_error: '网络超时',
              ocr_address_match_error: '',
              file_path: '',
              hostname: 'test',
              other_data:
                '{"time":{"remind_type":100,"ocr_3code":100,"ocr_address":100,"api_3code":100,"api_address":100,"balance_msg":100,"time": 100},"config":{"type":"1","isDao":"1","matchType":["1"],"get3CodeType":"ocr_third","logistics":{"type":"0","time":null,"sTime":null,"eTime":null},"fourCode":"0"}}',
              create_at: '2025-05-08 15:38:31',
              file_path_online: 1,
            },
          },
        ],
        yz_data_list: [
          {
            id: '218',
            dak_id: '3473051',
            express_type: '-1',
            waybill_no: '1231584929543',
            brand: 'ems',
            pickup_code: '1-1-1015',
            express_phone: '15201946772',
            topic_id: '5782398',
            status: '3',
            operate_status: '2',
            pickup_code_level: '1',
            pickup_code_shelf: '1',
            pickup_code_last: '1015',
            created_time: '2025-05-06 13:21:44',
            img_url: '',
            img_stat: 0,
            brand_cn: 'EMS',
            phone: '15201946772',
            status_cn: '已出库',
          },
        ],
      },
    });
  },
  'POST /Api/SuperSearch/getYzDetails': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {
        list: [],
      },
    });
  },
  'POST /Api/Automation/SortLine/fetchPic': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {
        list: [],
      },
    });
  },
  'POST /Api/Automation/SortLine/getChannelList': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: [
        { name: '快递员APP（扫一扫）', value: '1' },
        { name: '狂扫自动化', value: '2' },
        { name: '新零售补件', value: '3' },
        { name: '自动化分拣', value: '4' },
        { name: '快递员APP极简模式自动化到派', value: '6' },
        { name: '快递员APP极简模式自动化派件', value: '7' },
      ],
    });
  },
};
